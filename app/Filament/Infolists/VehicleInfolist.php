<?php

namespace App\Filament\Infolists;

use App\Enums\Vehicles\VehicleStatus;
use App\Enums\VehicleTypesCategories;
use App\Livewire\Vehicles\Equipments;
use Filament\Infolists\Components\Fieldset;
use Filament\Infolists\Components\Grid as InfoListGrid;
use Filament\Infolists\Components\Group;
use Filament\Infolists\Components\IconEntry;
use Filament\Infolists\Components\ImageEntry;
use Filament\Infolists\Components\Livewire;
use Filament\Infolists\Components\Section as InfolistSection;
use Filament\Infolists\Components\Split;
use Filament\Infolists\Components\Tabs;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Components\TextEntry\TextEntrySize;
use Filament\Infolists\Infolist;
use Illuminate\Support\Str;

class VehicleInfolist
{
    public static function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->columns(1)
            ->schema([
                Tabs::make('Vehicle Details')
                    ->tabs([
                        Tabs\Tab::make('Vehicle Informations')
                            ->icon('mdi-car-info')
                            ->schema([
                                InfolistSection::make('Vehicle Informations')
                                    ->schema([
                                        Split::make([
                                            InfoListGrid::make(3)
                                                ->schema([
                                                    Group::make([

                                                        TextEntry::make('vehicleType.category.value')
                                                            ->badge()
                                                            ->getStateUsing(fn ($record) => $record->vehicleType?->category?->value ?: 'Category not specified')
                                                            ->formatStateUsing(function ($state) {
                                                                // Check if the category value is 'passenger'
                                                                if ($state === 'passenger') {
                                                                    return 'أشخاص';
                                                                } else {
                                                                    return 'بضائع';
                                                                }
                                                            })
                                                            ->label('Category'),

                                                        TextEntry::make('license_plate_number')
                                                            ->badge()
                                                            ->getStateUsing(fn ($record) => $record->license_plate_number ?: 'License plate number not specified')
                                                            ->label('License Plate Number'),

                                                        TextEntry::make('vehicleType.name_ar')
                                                            ->label('Type Name (Arabic)')
                                                            ->icon('heroicon-m-truck')
                                                            ->badge()
                                                            ->getStateUsing(fn ($record) => empty($record->vehicleType?->name_ar) ||
                                                                in_array($record->vehicleType?->name_en, ['Default_passenger_type', 'Default_freight_type'])
                                                                    ? 'الاسم غير متوفر'
                                                                    : $record->vehicleType->name_ar
                                                            ),

                                                        TextEntry::make('vehicleType.name_en')
                                                            ->label('Type Name (English)')
                                                            ->icon('heroicon-m-truck')
                                                            ->badge()
                                                            ->getStateUsing(fn ($record) => empty($record->vehicleType?->name_en) || in_array($record->vehicleType?->name_en, ['Default_passenger_type', 'Default_freight_type']) ? 'Type name not available' : $record->vehicleType->name_en),

                                                        TextEntry::make('vehicleModel.vehicleBrand.name_ar')
                                                            ->label('Brand Name (Arabic)')
                                                            ->icon('heroicon-m-truck')
                                                            ->badge()
                                                            ->getStateUsing(fn ($record) => $record->vehicleModel?->vehicleBrand?->name_ar ?: 'Brand name not specified'),

                                                        TextEntry::make('vehicleModel.vehicleBrand.name_en')
                                                            ->label('Brand Name (English)')
                                                            ->icon('heroicon-m-truck')
                                                            ->badge()
                                                            ->getStateUsing(fn ($record) => $record->vehicleModel?->vehicleBrand?->name_en ?: 'Brand name not specified'),

                                                        TextEntry::make('vehicleModel.name_ar')
                                                            ->label('Model Name (Arabic)')
                                                            ->icon('heroicon-m-truck')
                                                            ->badge()
                                                            ->getStateUsing(fn ($record) => $record->vehicleModel?->name_ar ?: 'Model name not available'),

                                                        TextEntry::make('vehicleModel.name_en')
                                                            ->label('Model Name (English)')
                                                            ->icon('heroicon-m-truck')
                                                            ->badge()
                                                            ->getStateUsing(fn ($record) => $record->vehicleModel?->name_en ?: 'Model name not available'),

                                                        TextEntry::make('seat_number')
                                                            ->label('Number of Seats')
                                                            ->getStateUsing(fn ($record) => $record->seat_number ?: 'Seat number not specified')
                                                            ->visible(fn ($record) => $record->vehicleType?->category === VehicleTypesCategories::Passenger),

                                                        IconEntry::make('vehicleType.is_covered')
                                                            ->label('Type is Covered')
                                                            ->size(IconEntry\IconEntrySize::TwoExtraLarge)
                                                            ->boolean()
                                                            ->visible(fn ($record) => $record->vehicleType?->category === VehicleTypesCategories::Freight),

                                                        TextEntry::make('vehicleType.weight_category')
                                                            ->size(TextEntrySize::Large)
                                                            ->label('Weight Category (Arabic)')
                                                            ->badge()
                                                            ->getStateUsing(fn ($record) => $record->vehicleType?->weight_category ?: 'Weight category not assigned')
                                                            ->formatStateUsing(function ($state) {
                                                                // Check if the category value is 'passenger'
                                                                if ($state->value === 'less_than_1000kg') {
                                                                    return 'أقل من 1000 كجم';
                                                                } else {
                                                                    return 'أكثر من 1000 كجم';
                                                                }
                                                            })
                                                            ->visible(fn ($record) => $record->vehicleType?->category === VehicleTypesCategories::Freight),

                                                        TextEntry::make('vehicleType.weight_category')
                                                            ->size(TextEntrySize::Large)
                                                            ->label('Weight Category (English)')
                                                            ->badge()
                                                            ->getStateUsing(fn ($record) => $record->vehicleType?->weight_category ?: 'Weight category not assigned')
                                                            ->visible(fn ($record) => $record->vehicleType?->category === VehicleTypesCategories::Freight),

                                                        TextEntry::make('color')
                                                            ->label('Color')
                                                            ->icon('heroicon-m-swatch')
                                                            ->getStateUsing(fn ($record) => $record->color ?: 'Color not specified'),

                                                        TextEntry::make('year')
                                                            ->label('Year')
                                                            ->icon('heroicon-m-calendar-days')
                                                            ->getStateUsing(fn ($record) => $record->year ?: 'Year not provided'),

                                                        TextEntry::make('global_status')
                                                            ->label('Vehicle status')
                                                            ->default('No status available')
                                                            ->badge(),

                                                    ])->columnSpan(3)->columns(2),
                                                ]),
                                        ])->from('lg'),
                                    ]),

                                InfolistSection::make('Vehicle Documents')
                                    ->schema([
                                        // Car Image (Full Width)
                                        ImageEntry::make('image')
                                            ->defaultImageUrl(url('/images/vehicle.jpg'))
                                            ->label('Car Image')
                                            ->columnSpanFull(),

                                        // Insurance Fieldset
                                        Fieldset::make('')
                                            ->schema([
                                                TextEntry::make('documents.insurance')
                                                    ->label('Insurance Certificate')
                                                    ->formatStateUsing(function ($state, $record) {
                                                        $url = $record->documents?->insurance ? asset('storage/'.$record->documents->insurance) : null;

                                                        return $url ? "<a href='{$url}' target='_blank' class='text-blue-600 hover:text-blue-800 underline'>View Insurance Certificate</a>" : 'No Insurance Certificate Available';
                                                    })
                                                    ->html(),

                                                TextEntry::make('documents.insurance_expiry')
                                                    ->label('Expiry Date')
                                                    ->getStateUsing(fn ($record) => $record->documents?->insurance_expiry ?: 'No expiry date provided')
                                                    ->color(fn ($state) => $state === 'No expiry date provided' ? 'gray' : (now()->parse($state)->isPast() ? 'danger' : 'success'))
                                                    ->icon(fn ($state) => $state === 'No expiry date provided' ? 'heroicon-o-calendar' : (now()->parse($state)->isPast() ? 'heroicon-o-exclamation-circle' : 'heroicon-o-check-circle')),
                                            ]),

                                        // Technical Inspection Fieldset
                                        Fieldset::make('')
                                            ->schema([
                                                TextEntry::make('documents.technical_inspection')
                                                    ->label('Technical Inspection')
                                                    ->formatStateUsing(function ($state, $record) {
                                                        $url = $record->documents?->technical_inspection ? asset('storage/'.$record->documents->technical_inspection) : null;

                                                        return $url ? "<a href='{$url}' target='_blank' class='text-blue-600 hover:text-blue-800 underline'>View Technical Inspection</a>" : 'No Technical Inspection Available';
                                                    })
                                                    ->html(),

                                                TextEntry::make('documents.technical_inspection_expiry')
                                                    ->label('Expiry Date')
                                                    ->getStateUsing(fn ($record) => $record->documents?->technical_inspection_expiry ?: 'No expiry date provided')
                                                    ->color(fn ($state) => $state === 'No expiry date provided' ? 'gray' : (now()->parse($state)->isPast() ? 'danger' : 'success'))
                                                    ->icon(fn ($state) => $state === 'No expiry date provided' ? 'heroicon-o-calendar' : (now()->parse($state)->isPast() ? 'heroicon-o-exclamation-circle' : 'heroicon-o-check-circle')),
                                            ]),

                                        // Roaming Permit Fieldset
                                        Fieldset::make('')
                                            ->schema([
                                                TextEntry::make('documents.roaming_permit')
                                                    ->label('Roaming Permit')
                                                    ->formatStateUsing(function ($state, $record) {
                                                        $url = $record->documents?->roaming_permit ? asset('storage/'.$record->documents->roaming_permit) : null;

                                                        return $url ? "<a href='{$url}' target='_blank' class='text-blue-600 hover:text-blue-800 underline'>View Roaming Permit</a>" : 'No Roaming Permit Available';
                                                    })
                                                    ->html(),

                                                TextEntry::make('documents.roaming_permit_expiry')
                                                    ->label('Expiry Date')
                                                    ->getStateUsing(fn ($record) => $record->documents?->roaming_permit_expiry ?: 'No expiry date provided')
                                                    ->color(fn ($state) => $state === 'No expiry date provided' ? 'gray' : (now()->parse($state)->isPast() ? 'danger' : 'success'))
                                                    ->icon(fn ($state) => $state === 'No expiry date provided' ? 'heroicon-o-calendar' : (now()->parse($state)->isPast() ? 'heroicon-o-exclamation-circle' : 'heroicon-o-check-circle')),
                                            ]),
                                    ])
                                    ->columns(1) // Single column layout for better readability
                                    ->collapsible(),

                                InfolistSection::make('Driver Details')
                                    ->schema([
                                        TextEntry::make('drivers.user.full_name')
                                            ->label('Driver Name')
                                            // ->getStateUsing(fn ($record) => $record->user ? $record->user->full_name : 'Driver name not provided')
                                            ->icon('heroicon-m-user'),

                                        TextEntry::make('is_female_only')
                                            ->label('Women Services')
                                            ->badge()
                                            ->state('Active')
                                            ->color('success')
                                            ->visible(fn ($record) => $record->drivers->first()?->user?->gender?->value === 'female'),

                                        TextEntry::make('notes')
                                            ->getStateUsing(fn ($record) => $record->notes ? $record->notes : 'No notes provided')
                                            ->icon('heroicon-m-pencil-square')
                                            ->label('Notes'),
                                    ])
                                    ->columns(2)
                                    ->collapsible(),

                                InfolistSection::make('Rejection Information')
                                    ->description('The following fields/documents were rejected and need to be updated.')
                                    ->schema([
                                        TextEntry::make('rejection_reason_columns')
                                            ->label('Rejected Fields')
                                            ->formatStateUsing(function ($state) {
                                                if (empty($state)) {
                                                    return 'No rejection details available';
                                                }

                                                // Map field names to user-friendly labels
                                                $fieldLabels = [
                                                    'license_plate_number' => 'License Plate Number',
                                                    'vehicle_brand' => 'Vehicle Brand',
                                                    'vehicle_model' => 'Vehicle Model',
                                                    'year' => 'Year',
                                                    'color' => 'Color',
                                                    'seat_number' => 'Number of Seats',
                                                    'is_covered' => 'Covered/Open (Freight)',
                                                    'weight_category' => 'Weight Category (Freight)',
                                                    'image' => 'Car Image',
                                                    'insurance' => 'Insurance Certificate',
                                                    'technical_inspection' => 'Technical Inspection',
                                                    'roaming_permit' => 'Roaming Permit',
                                                ];

                                                $rejectedFields = array_map(function ($field) use ($fieldLabels) {
                                                    return $fieldLabels[$field] ?? $field;
                                                }, $state);

                                                return '<ul class="list-disc list-inside space-y-1">'.
                                                    implode('', array_map(fn ($field) => "<li class='text-red-600'>$field</li>", $rejectedFields)).
                                                    '</ul>';
                                            })
                                            ->html(),

                                        TextEntry::make('rejection_reason')
                                            ->label('Rejection Reason')
                                            ->getStateUsing(fn ($record) => $record->rejection_reason ?: 'No rejection reason provided')
                                            ->color('danger')
                                            ->icon('heroicon-m-exclamation-triangle'),
                                    ])
                                    ->visible(fn ($record) => $record->previous_global_status === VehicleStatus::rejected->value ||
                                        $record->global_status === VehicleStatus::rejected
                                    )
                                    ->columns(1)
                                    ->collapsible(),
                            ]),

                        Tabs\Tab::make('Vehicle Equipments')
                            ->icon('heroicon-o-squares-plus')
                            ->schema(function ($record) {
                                return [
                                    Livewire::make(Equipments::class, ['vehicle' => $record])->key(Str::random()),
                                ];
                            })->visible(fn ($record) => $record->vehicleType?->category->value === 'passenger'),
                    ]),
            ]);
    }
}
