<?php

namespace App\Filament\Tables;

use App\Filament\Resources\Panel\DriverResource;
use App\Models\Vehicle;
use App\Models\VehicleBrand;
use App\Models\VehicleModel;
use App\Models\VehicleType;
use Filament\Forms\Components\Select;
use Filament\Notifications\Notification;
use Filament\Support\Enums\MaxWidth;
use Filament\Tables;
use Filament\Tables\Actions\Action;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use IbrahimBougaoua\FilamentRatingStar\Columns\Components\RatingStar as ColumnRatingStar;
use IbrahimBougaoua\FilamentRatingStar\Forms\Components\RatingStar as FormRatingStar;
use Illuminate\Database\Eloquent\Builder;

class VehiclesTable
{
    public static function make(Table $table, ?Builder $query = null, string $context = 'default'): Table
    {
        return $table
            ->poll('60s')
            ->query(query: $query ?? \App\Models\Vehicle::query())
            ->columns([

                TextColumn::make('license_plate_number')
                    ->label('License Plate Number')
                    ->searchable($context !== 'drivers'),

                TextColumn::make('drivers.user.full_name')
                    ->label('Driver Name')
                    ->url(function ($record) {
                        return $record->drivers->isNotEmpty()
                            ? DriverResource::getUrl('view', ['record' => $record->drivers->first()])
                            : null;
                    })
                    ->searchable($context !== 'drivers'),

                TextColumn::make('vehicleModel.vehicleBrand.name_en')
                    ->label('Brand'),

                TextColumn::make('vehicleModel.name_en')
                    ->label('Model'),

                TextColumn::make('vehicleType.name_en')
                    ->label('Type')
                    ->getStateUsing(fn ($record) => empty($record->vehicleType?->name_en) ||
                        in_array($record->vehicleType?->name_en, ['Default_passenger_type', 'Default_freight_type'])
                            ? 'Not available'
                            : $record->vehicleType->name_en
                    ),

                TextColumn::make('vehicleType.category.value')
                    ->label('Category')
                    ->formatStateUsing(function ($state) {
                        // Always return the category with proper capitalization
                        return $state === 'passenger' ? 'Passenger' : ($state === 'freight' ? 'Freight' : $state);
                    }),

                ColumnRatingStar::make('average_vehicle_rating')
                    ->label('Ratings')
                    ->size('sm'),

                TextColumn::make('global_status')
                    ->label('Status')
                    ->badge(),

            ])
            ->filters(
                [
                    Filter::make('vehicle')
                        ->form([
                            Select::make('vehicle_brand')
                                ->label('Vehicle Brand')
                                ->options(fn () => VehicleBrand::pluck('name_en', 'id')->toArray())
                                ->searchable()
                                ->preload()
                                ->live()
                                ->reactive()
                                ->afterStateUpdated(function ($state, callable $get, callable $set) {
                                    if ($state) {
                                        $models = VehicleModel::where('vehicle_brand_id', $state)
                                            ->pluck('name_en', 'id')
                                            ->toArray();

                                        $set('vehicle_model', null);
                                        $set('vehicle_model_options', $models);
                                    } else {
                                        $set('vehicle_model', null);
                                        $set('vehicle_model_options', VehicleModel::pluck('name_en', 'id')->toArray());
                                    }
                                }),
                            Select::make('vehicle_model')
                                ->label('Vehicle Model')
                                ->options(fn (callable $get) => $get('vehicle_model_options') ?? VehicleModel::pluck('name_en', 'id')->toArray()
                                )
                                ->searchable()
                                ->preload()
                                ->live()
                                ->reactive()
                                ->afterStateUpdated(function ($state, callable $get, callable $set) {
                                    if ($state) {
                                        $model = VehicleModel::find($state);
                                        if ($model) {
                                            $set('vehicle_brand', $model->vehicle_brand_id);
                                        }
                                    }
                                }),
                        ])
                        ->query(function (Builder $query, array $data) {
                            if (! empty($data['vehicle_brand'])) {
                                $query->whereHas('vehicleModel.vehicleBrand', fn ($q) => $q->where('id', $data['vehicle_brand']));
                            }

                            if (! empty($data['vehicle_model'])) {
                                $query->whereHas('vehicleModel', fn ($q) => $q->where('id', $data['vehicle_model']));
                            }
                        })
                        ->indicateUsing(function (array $data): array {
                            $indicators = [];

                            if (! empty($data['vehicle_brand'])) {
                                $brand = VehicleBrand::find($data['vehicle_brand']);
                                if ($brand) {
                                    $indicators[] = 'Brand: '.$brand->name_en;
                                }
                            }

                            if (! empty($data['vehicle_model'])) {
                                $model = VehicleModel::find($data['vehicle_model']);
                                if ($model) {
                                    $indicators[] = 'Model: '.$model->name_en;
                                }
                            }

                            return $indicators;

                        }),

                    SelectFilter::make('vehicle_type_id')
                        ->label('Vehicle Type')
                        ->options(fn () => VehicleType::where('name_en', '!=', 'Default_passenger_type')
                            ->where('name_en', '!=', 'Default_freight_type')
                            ->pluck('name_en', 'id')
                            ->toArray()
                        )
                        ->searchable()
                        ->preload(),

                    SelectFilter::make('number_of_seats')
                        ->label('Number of Seats')
                        ->native(false)
                        ->options([
                            2 => '2 Seats',
                            4 => '4 Seats',
                            6 => '6 Seats',
                        ])
                        ->query(function (\Illuminate\Database\Eloquent\Builder $query, array $data) {
                            if (isset($data['value'])) {
                                $query->whereHas('vehicleType', function (\Illuminate\Database\Eloquent\Builder $query) use ($data) {
                                    $query->where('seat_number', $data['value']);
                                });
                            }
                        }),

                    SelectFilter::make('vehicle_equipment_id')
                        ->label('Specific Equipment')
                        ->relationship('vehicleEquipments', 'name_en', function ($query) {
                            $query->where('status', 1)
                                ->orderBy('vehicle_equipment.created_at', 'desc'); // Specify the table name
                        })
                        ->searchable()
                        ->preload(),

                    // Filter::make('rider_to_car_rating')
                    //     ->form([
                    //         FormRatingStar::make('rider_to_car_rating')
                    //             ->label('Star rating'),
                    //     ])
                    //     ->query(function ($query, $data) {
                    //         if (isset($data['rider_to_car_rating'])) {
                    //             $query->whereHas('trips.tripRatings', function ($q) use ($data) {
                    //                 $q->where('rider_to_car_rating', $data['rider_to_car_rating']);
                    //             });
                    //         }
                    //     })
                    //     ->indicateUsing(function (array $data): ?string {
                    //         if (! isset($data['rider_to_car_rating'])) {
                    //             return null;
                    //         }

                    //         return 'Star rating: '.$data['rider_to_car_rating'];
                    //     }),
                    Filter::make('average_vehicle_rating')
                        ->form([
                            FormRatingStar::make('average_vehicle_rating')
                                ->label('Ratings'),
                        ])
                        ->query(function ($query, array $data) {
                            return $query->when(
                                $data['average_vehicle_rating'],
                                function ($q, $rating) {
                                    $selectedRating = (int) $rating;

                                    // Filter ratings within the selected star range
                                    // For example: selecting 2 stars shows ratings from 2.0 to 2.99
                                    return $q->where('average_vehicle_rating', '>=', $selectedRating)
                                        ->where('average_vehicle_rating', '<', $selectedRating + 1);
                                }
                            );
                        })
                        ->indicateUsing(function (array $data) {
                            if (! $data['average_vehicle_rating']) {
                                return [];
                            }

                            $selectedRating = (int) $data['average_vehicle_rating'];

                            return ["Ratings: {$selectedRating}.0 - {$selectedRating}.99 stars"];
                        }),

                ]
            )
            ->filtersFormWidth(MaxWidth::Small)
            ->filtersTriggerAction(
                fn (Action $action) => $action
                    ->slideOver()
                    ->modalIcon('heroicon-o-funnel')
                    ->button()
                    ->size('md')
                    ->icon('heroicon-o-funnel')
                    ->label('Filter'),
            )
            ->actions([

                // Tables\Actions\EditAction::make(),
                Tables\Actions\ViewAction::make(),
                // Tables\Actions\DeleteAction::make()
                //     ->successNotification(
                //         Notification::make()
                //             ->success()
                //             ->title('Vehicle deleted successfully')
                //     ),
            ])
            ->bulkActions([
                // Tables\Actions\BulkActionGroup::make([
                //     Tables\Actions\DeleteBulkAction::make(),
                // ]),
            ])
            ->paginated($context !== 'drivers')
            ->paginationPageOptions([5, 10, 25, 50])
            ->defaultSort('created_at', 'desc');
    }
}
