<?php

namespace App\Filament\Tables;

use App\Enums\Trips\TripStatus;
use App\Filament\Tables\Filters\TripStatusTabFilter;
use App\Filament\Traits\HasDateRangeFilter;
use App\Http\Controllers\Api\RidersTripController;
use App\Services\Pricing\PricingResult;
use App\Traits\HasTripStatusColumn;
use Filament\Forms\Components\Select;
use Filament\Notifications\Notification;
use Filament\Tables;
use Filament\Tables\Actions\Action;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Filters\Indicator;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;

class TripsTable
{
    use HasDateRangeFilter ,HasTripStatusColumn;

    public static function make(Table $table): Table
    {
        return $table
            ->poll('60s')
            ->modifyQueryUsing(fn (Builder $query) => $query->with('cancellation'))
            ->columns([
                TextColumn::make('id')
                    ->label('Trip Id')
                    ->searchable(),

                TextColumn::make('created_at')
                    ->date()
                    ->label('Date')
                    ->visible(function ($livewire) {
                        return $livewire instanceof \App\Filament\Resources\Panel\TripResource\Pages\ListTrips
                            && $livewire->activeTab === 'all';
                    }),

                TextColumn::make('created_at_time')
                    ->label('Time')
                    ->getStateUsing(fn ($record) => $record->created_at->format('H:i:s'))
                    ->visible(function ($livewire) {
                        return $livewire instanceof \App\Filament\Resources\Panel\TripResource\Pages\ListTrips
                            && $livewire->activeTab === 'all';
                    }),

                TextColumn::make('driver.user.full_name')
                    // ->url(fn ($record) => DriverResource::getUrl('view', ['record' => $record->driver]))
                    ->label('Driver Name')
                    ->searchable(),
                TextColumn::make('driver.user.phone_number')
                    ->label('Driver Phone')
                    ->searchable()
                    ->toggleable(isToggledHiddenByDefault: true),

                TextColumn::make('rider.user.full_name')
                    // ->url(fn ($record) => RiderResource::getUrl('view', ['record' => $record->rider]))
                    ->label('Rider Name')
                    ->searchable(),
                TextColumn::make('rider.user.phone_number')
                    ->label('Rider Phone')
                    ->searchable()
                    ->toggleable(isToggledHiddenByDefault: true),

                self::getTripStatusColumn('status', 'Trip Status'),

                TextColumn::make('tripLocation.departure_address')
                    ->label('Pickup location')
                    ->default('-')
                    ->limit(15)
                    ->tooltip(function (TextColumn $column): ?string {
                        $state = $column->getState();

                        if (strlen($state) <= $column->getCharacterLimit()) {
                            return null;
                        }

                        // Only render the tooltip if the column content exceeds the length limit.
                        return $state;
                    }),

                TextColumn::make('tripLocation.arrival_address')
                    ->label('Drop-off location')
                    ->default('-')
                    ->limit(15)
                    ->tooltip(function (TextColumn $column): ?string {
                        $state = $column->getState();

                        if (strlen($state) <= $column->getCharacterLimit()) {
                            return null;
                        }

                        // Only render the tooltip if the column content exceeds the length limit.
                        return $state;
                    }),

                TextColumn::make('fare')
                    ->getStateUsing(function ($record) {
                        // Check if pricing_breakdown exists and is valid JSON
                        if (! $record->pricing_breakdown) {
                            return '';
                        }

                        $total = json_decode($record->pricing_breakdown, true);

                        // Check if total exists in the decoded JSON
                        if (! $total || ! isset($total['total'])) {
                            return '';
                        }

                        // Use PricingResult to apply rounding
                        $pricingResult = new PricingResult;
                        $pricingResult->setTotal($total['total']);
                        $pricingResult->applyRounding();

                        // Get the rounded total from the pricing result
                        $roundedTotal = $pricingResult->toArray()['total'];

                        return $roundedTotal.' LYD';
                    })
                    ->label('Fare'),

                TextColumn::make('estimated_arrival_time')
                    ->label('ETA')
                    ->visible(function ($livewire) {
                        return $livewire instanceof \App\Filament\Resources\Panel\TripResource\Pages\ListTrips
                            && $livewire->activeTab === 'current';
                    }),
            ])
            ->paginationPageOptions([5, 10, 25, 50])
            ->filters([
                self::dateRangeFilter(),
                TripStatusTabFilter::make('status')
                    ->label('Trip Status'),

                Filter::make('address')
                    ->form([
                        Select::make('address_search')
                            ->label('Location')
                            ->options(function () {
                                // Get unique addresses from trip_locations table (limit to most recent 100)
                                $departureAddresses = \App\Models\TripLocation::query()
                                    ->whereNotNull('departure_address')
                                    ->orderBy('created_at', 'desc')
                                    ->limit(100)
                                    ->pluck('departure_address')
                                    ->unique()
                                    ->toArray();

                                $arrivalAddresses = \App\Models\TripLocation::query()
                                    ->whereNotNull('arrival_address')
                                    ->orderBy('created_at', 'desc')
                                    ->limit(100)
                                    ->pluck('arrival_address')
                                    ->unique()
                                    ->toArray();

                                // Combine and remove duplicates
                                $allAddresses = array_unique(array_merge($departureAddresses, $arrivalAddresses));

                                // Sort alphabetically for better usability
                                sort($allAddresses);

                                // Format as key-value pairs for the select (limit to 200 total)
                                $allAddresses = array_slice($allAddresses, 0, 200);

                                return array_combine($allAddresses, $allAddresses);
                            })
                            ->searchable()
                            ->multiple(),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        if (empty($data['address_search'])) {
                            return $query;
                        }

                        return $query->whereHas('tripLocation', function ($q) use ($data) {
                            $q->where(function ($subQuery) use ($data) {
                                foreach ($data['address_search'] as $address) {
                                    $subQuery->orWhere('departure_address', 'like', "%{$address}%")
                                        ->orWhere('arrival_address', 'like', "%{$address}%");
                                }
                            });
                        });
                    })
                    ->indicateUsing(function (array $data): array {
                        if (empty($data['address_search'])) {
                            return [];
                        }

                        $addresses = implode(', ', $data['address_search']);

                        return [
                            Indicator::make("Location: {$addresses}")
                                ->removeField('address_search'),
                        ];
                    }),

            ])
            ->filtersTriggerAction(
                fn (Action $action) => $action
                    ->slideOver()
                    ->modalIcon('heroicon-o-funnel')
                    ->button()
                    ->size('md')
                    ->icon('heroicon-o-funnel')
                    ->label('Filter')
            )
            ->actions([
                Tables\Actions\ActionGroup::make([
                    // Tables\Actions\EditAction::make(),
                    Tables\Actions\ViewAction::make(),

                    Action::make('cancel')
                        ->label('Cancel')
                        ->icon('heroicon-o-x-mark')
                        ->color('danger')
                        ->visible(fn ($record) => in_array($record->status->value, [
                            TripStatus::pending->value,
                            TripStatus::dispatched->value,
                        ]))
                        ->requiresConfirmation()
                        ->modalHeading('Cancel Trip')
                        ->modalDescription('Are you sure you want to cancel this trip?')
                        ->action(function ($record, array $data) {
                            try {
                                $currentUser = Auth::user();

                                // Use the existing cancellation controller
                                $tripController = app(RidersTripController::class);

                                $result = $tripController->cancelTrip(
                                    $record->id,
                                    $currentUser->id,
                                    'admin',
                                    'Cancelled by admin'
                                );

                                if ($result['success']) {
                                    Notification::make()
                                        ->title('Trip Cancelled Successfully')
                                        ->body('The trip has been cancelled.')
                                        ->success()
                                        ->send();
                                } else {
                                    Notification::make()
                                        ->title('Cancellation Failed')
                                        ->body($result['message'] ?? 'Failed to cancel the trip.')
                                        ->danger()
                                        ->send();
                                }
                            } catch (\Exception $e) {
                                Notification::make()
                                    ->title('Error')
                                    ->body('An error occurred while cancelling the trip: '.$e->getMessage())
                                    ->danger()
                                    ->send();
                            }
                        }),
                ]),
            ])
            ->defaultSort('created_at', 'desc')
            ->paginationPageOptions([5, 10, 25, 50]);
    }
}
