<?php

namespace App\Filament\Resources\Panel;

use App\Enums\Drivers\DriverGlobalStatus;
use App\Filament\Resources\Panel\DriverResource\Pages;
use App\Filament\Resources\Panel\DriverResource\RelationManagers;
use App\Filament\Tables\DriversTable;
use App\Helpers\TripHelper;
use App\Livewire\Drivers\Trips;
use App\Livewire\Drivers\Vehicles;
use App\Models\Driver;
use App\Rules\UniqueTransformedPhoneNumber;
use Cheesegrits\FilamentGoogleMaps\Fields\Map;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Fieldset;
use Filament\Forms\Components\Fieldset as ComponentsFieldset;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Form;
use Filament\Infolists\Components\Grid as InfoListGrid;
use Filament\Infolists\Components\Group;
use Filament\Infolists\Components\ImageEntry;
use Filament\Infolists\Components\Section as InfolistSection;
use Filament\Infolists\Components\Split;
use Filament\Infolists\Components\Tabs;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Infolist;
use Filament\Notifications\Notification;
use Filament\Resources\Resource;
use Filament\Tables\Table;
use IbrahimBougaoua\FilamentRatingStar\Entries\Components\RatingStar as InfolistRatingStar;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Carbon;
use Illuminate\Support\HtmlString;
use Illuminate\Support\Str;
use Ysfkaya\FilamentPhoneInput\Infolists\PhoneEntry;
use Ysfkaya\FilamentPhoneInput\PhoneInputNumberType;

class DriverResource extends Resource
{
    protected static ?string $model = Driver::class;

    protected static ?string $navigationIcon = 'heroicon-o-users';

    protected static ?string $navigationGroup = 'Admin';

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                // Section 1: Personal Information
                Section::make('Personal Information')
                    ->description('Update driver contact details and profile photo')
                    ->icon('heroicon-o-user-circle')
                    ->schema([
                        Grid::make(2)
                            ->schema([

                                TextInput::make('phone_number')
                                    ->label(__('Phone Number'))
                                    ->prefix(new HtmlString('<img src="'.asset('images/lybia.png').'" alt="Libya Flag" class="w-6 h-4 inline mr-1"> '))
                                    ->extraInputAttributes([
                                        'inputmode' => 'numeric',
                                        'pattern' => '[0-9-]*',
                                        'maxlength' => 10,
                                        'oninput' => "this.value = this.value.replace(/[^0-9]/g, '').slice(0, 9); if (this.value.length > 2) this.value = this.value.slice(0, 2) + '-' + this.value.slice(2);",
                                    ])
                                    ->rules(function ($livewire) {
                                        $rules = [
                                            'required',
                                            'regex:/^(91|92|93|94|95)-\d{7}$/',
                                        ];
                                        if ($livewire instanceof Pages\EditDriver && $livewire->record && $livewire->record->user) {
                                            $rules[] = new UniqueTransformedPhoneNumber($livewire->record->user->id);
                                        } else {
                                            $rules[] = new UniqueTransformedPhoneNumber;
                                        }

                                        return $rules;
                                    })
                                    ->helperText('Must start with 91, 92, 93, 94, or 95 followed by seven digits.')
                                    ->placeholder('00-0000000'),

                                TextInput::make('address')
                                    ->label('Address')
                                    ->rules(['required', 'max:60'])
                                    ->maxLength(60)
                                    ->debounce(100)
                                    ->markAsRequired(),
                                ComponentsFieldset::make()
                                    ->schema([
                                        Toggle::make('global_status')
                                            ->label('Unblock Driver')
                                            ->reactive()
                                            ->offIcon('mdi-restore')
                                            ->onIcon('heroicon-o-no-symbol')
                                            ->inline(false)
                                            ->hidden(fn ($record) => $record->global_status !== DriverGlobalStatus::blocked)
                                            ->offColor('warning')
                                            ->onColor('danger')
                                            ->beforeStateDehydrated(fn ($state, $record) => $state = ($record->global_status === DriverGlobalStatus::blocked))
                                            ->afterStateUpdated(function ($state, $record, callable $set) {
                                                // Only validate when trying to unblock (state = false when toggled off)
                                                if (! $state && $record && TripHelper::driverHasActiveTrips($record)) {
                                                    Notification::make()
                                                        ->title('Unblocking Prevented')
                                                        ->body('This driver cannot be unblocked because they have active trips in progress.')
                                                        ->warning()
                                                        ->send();

                                                    // Revert the toggle state
                                                    $set('global_status', true);
                                                }
                                            })
                                            ->helperText('Toggle to unblock this driver'),

                                        Toggle::make('block_toggle')
                                            ->label('Block Driver')
                                            ->reactive()
                                            ->inline(false)
                                            ->onIcon('heroicon-o-no-symbol')
                                            ->hidden(fn ($record) => $record->global_status !== DriverGlobalStatus::active)
                                            ->onColor('danger')
                                            ->afterStateUpdated(function ($state, $record, callable $set) {
                                                // Only validate when trying to block (state = true when toggled on)
                                                if ($state && $record && TripHelper::driverHasActiveTrips($record)) {
                                                    Notification::make()
                                                        ->title('Blocking Prevented')
                                                        ->body('This driver cannot be blocked because they have active trips in progress.')
                                                        ->warning()
                                                        ->send();

                                                    // Revert the toggle state
                                                    $set('block_toggle', false);
                                                }
                                            })
                                            ->helperText('Toggle to block this driver'),
                                    ])
                                    ->extraAttributes(['class' => 'border-none p-0'])
                                    ->hidden(fn ($record) => $record->global_status === DriverGlobalStatus::pending),

                                // Blocking Reason Section
                                Section::make([
                                    Textarea::make('blocking_reason')
                                        ->label('Reason For Blocking This Driver')
                                        ->rows(3)
                                        ->reactive()
                                        ->disabled(fn ($get) => ! $get('block_toggle'))
                                        ->rules(['required', 'max:60'])
                                        ->maxLength(60)
                                        ->cols(20)
                                        ->extraInputAttributes(fn ($get, $record) => $record->global_status->value == DriverGlobalStatus::blocked->value
        ? ['class' => 'bg-gray-100 dark:bg-gray-800']
        : []
                                        ),
                                ])->hidden(function ($get, $record) {
                                    // If driver is blocked but global_status toggle is false (meaning they want to unblock)
                                    if ($record->global_status->value == DriverGlobalStatus::blocked->value && ! $get('global_status')) {
                                        return true; // Hide the section
                                    }

                                    // For non-blocked drivers, only show when block_toggle is true
                                    if ($record->global_status->value != DriverGlobalStatus::blocked->value) {
                                        return ! $get('block_toggle');
                                    }

                                    return false; // Show in all other cases
                                }),
                            ]),

                        // Map and Hidden Fields
                        Grid::make()
                            ->extraAttributes(['class' => 'hideMap', 'style' => 'display:none;'])
                            ->schema([
                                Map::make('location')
                                    ->reactive()
                                    ->afterStateUpdated(function ($state, callable $get, callable $set) {
                                        logger()->info('Reverse Geocode Result:', [$state]);
                                        $set('latitude', $state['lat']);
                                        $set('longitude', $state['lng']);
                                    })
                                    ->live()
                                    ->geolocate()
                                    ->autocomplete('address', ['geocode'], countries: ['LY'])
                                    ->reverseGeocode([
                                        'street' => '%n %S',
                                        'city' => '%L',
                                        'state' => '%A1',
                                    ]),
                            ]),

                        Hidden::make('street'),
                        Hidden::make('city'),
                        Hidden::make('state'),
                        Hidden::make('latitude'),
                        Hidden::make('longitude'),

                    ]),

                // Section 2: Driver Documents
                Section::make('Driver Documents')
                    ->description('Upload and manage driver documents')
                    ->icon('heroicon-o-document-text')
                    ->schema([
                        Fieldset::make('')
                            ->relationship('documents')
                            ->schema([
                                Grid::make()
                                    ->schema([
                                        // Document Type Selection
                                        Section::make('Identity Document')
                                            ->description('Select and upload your identification document')
                                            ->icon('heroicon-o-identification')
                                            ->schema([
                                                Select::make('document_type')
                                                    ->required()
                                                    ->native(false)
                                                    ->label('')
                                                    ->placeholder('Select Driver Document Type')
                                                    ->options([
                                                        'personal_id_number' => 'Personal ID',
                                                        'passport' => 'Passport',
                                                        'national_id_number' => 'National ID',
                                                    ])
                                                    ->reactive()
                                                    ->afterStateHydrated(function ($state, callable $set) {
                                                        // This runs when the form loads with existing data
                                                        if ($state) {
                                                            $labels = [
                                                                'personal_id_number' => ['front' => 'Personal ID Front', 'back' => 'Personal ID Back'],
                                                                'passport' => ['front' => 'Passport Front', 'back' => 'Passport Back'],
                                                                'national_id_number' => ['front' => 'National ID Front', 'back' => 'National ID Back'],
                                                            ];
                                                            $set('document_front_label', $labels[$state]['front'] ?? 'Document Front');
                                                            $set('document_back_label', $labels[$state]['back'] ?? 'Document Back');
                                                        }
                                                    })
                                                    ->afterStateUpdated(function ($state, callable $set) {
                                                        $labels = [
                                                            'personal_id_number' => ['front' => 'Personal ID Front', 'back' => 'Personal ID Back'],
                                                            'passport' => ['front' => 'Passport Front', 'back' => 'Passport Back'],
                                                            'national_id_number' => ['front' => 'National ID Front', 'back' => 'National ID Back'],
                                                        ];
                                                        $set('document_front_label', $labels[$state]['front'] ?? 'Document Front');
                                                        $set('document_back_label', $labels[$state]['back'] ?? 'Document Back');
                                                    })
                                                    ->helperText('Select the type of document you are uploading.')
                                                    ->columnSpanFull(),

                                                // Hidden Fields for Dynamic Labels
                                                Hidden::make('document_front_label'),
                                                Hidden::make('document_back_label'),
                                            ]),

                                        // Document Uploads
                                        Section::make(function ($get) {
                                            return $get('document_type') ? 'Upload Document Images' : 'Document Images';
                                        })
                                            ->description('Upload front and back images of your selected document')
                                            ->icon('heroicon-o-document-duplicate')
                                            ->collapsible()
                                            ->schema([
                                                Grid::make(2)
                                                    ->schema([
                                                        FileUpload::make('document_front')
                                                            ->image()
                                                            ->maxFiles(1)
                                                            ->directory('driver_documents/document_front')
                                                            ->disk('public')
                                                            ->visibility('public')
                                                            ->appendFiles(false)
                                                            ->required()
                                                            ->previewable(true)
                                                            ->downloadable()
                                                            ->openable()
                                                            ->acceptedFileTypes(['image/jpeg', 'image/png', 'image/jpg', 'application/pdf'])
                                                            ->rules(['mimes:jpeg,png,jpg,pdf', 'max:5120'])
                                                            ->validationMessages([
                                                                'max' => 'The file must not be larger than 5MB.',
                                                            ])
                                                            ->maxSize(5120)
                                                            ->reactive()
                                                            ->removeUploadedFileButtonPosition('left')
                                                            ->afterStateUpdated(function ($state, $set) {
                                                                if ($state && in_array(pathinfo($state, PATHINFO_EXTENSION), ['svg'])) {
                                                                    $set('image', null);
                                                                    Notification::make()
                                                                        ->title('Invalid file type!')
                                                                        ->danger()
                                                                        ->body('SVG files are not allowed.')
                                                                        ->send();
                                                                }
                                                            })
                                                            ->label(function ($get) {
                                                                return $get('document_front_label') ?? 'Document Front';
                                                            })
                                                            ->helperText('Allowed: PNG, JPG, PDF. Max size: 5MB'),

                                                        FileUpload::make('document_back')
                                                            ->image()
                                                            ->maxFiles(1)
                                                            ->directory('driver_documents/document_back')
                                                            ->disk('public')
                                                            ->visibility('public')
                                                            ->appendFiles(false)
                                                            ->previewable(true)
                                                            ->downloadable()
                                                            ->openable()
                                                            ->acceptedFileTypes(['image/jpeg', 'image/png', 'image/jpg', 'application/pdf'])
                                                            ->rules(['mimes:jpeg,png,jpg,pdf', 'max:5120'])
                                                            ->validationMessages([
                                                                'max' => 'The file must not be larger than 5MB.',
                                                            ])
                                                            ->reactive()
                                                            ->maxSize(5120)
                                                            ->removeUploadedFileButtonPosition('left')
                                                            ->afterStateUpdated(function ($state, $set) {
                                                                if ($state && in_array(pathinfo($state, PATHINFO_EXTENSION), ['svg'])) {
                                                                    $set('image', null);
                                                                    Notification::make()
                                                                        ->title('Invalid file type!')
                                                                        ->danger()
                                                                        ->body('SVG files are not allowed.')
                                                                        ->send();
                                                                }
                                                            })
                                                            ->label(function ($get) {
                                                                return $get('document_back_label') ?? 'Document Back';
                                                            })
                                                            ->helperText('Allowed: PNG, JPG, PDF. Max size: 5MB')
                                                            ->required(function ($get) {
                                                                return $get('document_type') === 'national_id_number';
                                                            }),
                                                    ]),
                                            ]),

                                        // Driver's License Section
                                        Section::make('Driver\'s License')
                                            ->description('Upload your driver\'s license details')
                                            ->icon('heroicon-o-truck')
                                            ->collapsible()
                                            ->schema([
                                                Grid::make(2)
                                                    ->schema([
                                                        // License Front Upload
                                                        FileUpload::make('license_front')
                                                            ->image()
                                                            ->maxFiles(1)
                                                            ->directory('driver_documents/licence_front')
                                                            ->disk('public')
                                                            ->visibility('public')
                                                            ->appendFiles(false)
                                                            ->required()
                                                            ->previewable(true)
                                                            ->downloadable()
                                                            ->openable()
                                                            ->maxSize(5120)
                                                            ->acceptedFileTypes(['image/jpeg', 'image/png', 'image/jpg', 'application/pdf'])
                                                            ->rules(['mimes:jpeg,png,jpg,pdf', 'max:5120'])
                                                            ->validationMessages([
                                                                'max' => 'The file must not be larger than 5MB.',
                                                            ])
                                                            ->reactive()
                                                            ->removeUploadedFileButtonPosition('left')
                                                            ->afterStateUpdated(function ($state, $set) {
                                                                if ($state && in_array(pathinfo($state, PATHINFO_EXTENSION), ['svg'])) {
                                                                    $set('image', null);
                                                                    Notification::make()
                                                                        ->title('Invalid file type!')
                                                                        ->danger()
                                                                        ->body('SVG files are not allowed.')
                                                                        ->send();
                                                                }
                                                            })
                                                            ->label('License Front')
                                                            ->helperText('Upload the front side of the driver\'s license. Max size: 5MB'),

                                                        // License Back Upload
                                                        FileUpload::make('license_back')
                                                            ->image()
                                                            ->maxFiles(1)
                                                            ->directory('driver_documents/licence_back')
                                                            ->disk('public')
                                                            ->visibility('public')
                                                            ->appendFiles(false)
                                                            ->required()
                                                            ->previewable(true)
                                                            ->downloadable()
                                                            ->maxSize(5120)
                                                            ->openable()
                                                            ->acceptedFileTypes(['image/jpeg', 'image/png', 'image/jpg', 'application/pdf'])
                                                            ->rules(['mimes:jpeg,png,jpg,pdf', 'max:5120'])
                                                            ->validationMessages([
                                                                'max' => 'The file must not be larger than 5MB.',
                                                            ])
                                                            ->reactive()
                                                            ->removeUploadedFileButtonPosition('left')
                                                            ->afterStateUpdated(function ($state, $set) {
                                                                if ($state && in_array(pathinfo($state, PATHINFO_EXTENSION), ['svg'])) {
                                                                    $set('image', null);
                                                                    Notification::make()
                                                                        ->title('Invalid file type!')
                                                                        ->danger()
                                                                        ->body('SVG files are not allowed.')
                                                                        ->send();
                                                                }
                                                            })
                                                            ->label('License Back')
                                                            ->helperText('Upload the back side of the driver\'s license. Max size: 5MB'),

                                                        // License Expiry Date
                                                        DatePicker::make('license_expiry')
                                                            ->native(false)
                                                            ->required()
                                                            ->label('License Expiry Date')
                                                            ->placeholder('Select license expiry date')
                                                            ->minDate(now()->addMonths(4))
                                                            ->rules(['after_or_equal:'.now()->addMonths(4)->toDateString()])
                                                            ->columnSpanFull(),
                                                    ]),
                                            ]),
                                    ]),
                            ])->extraAttributes(['class' => 'border-none p-0'])
                            ->columns(1),
                    ]),

                // Hidden Fields
                Hidden::make('type')
                    ->default('driver')
                    ->dehydrated(),

                Hidden::make('password')
                    ->default('password'),
            ])->extraAttributes(['class' => 'custom-form-layout']);
    }

    public static function table(Table $table): Table
    {
        return DriversTable::make($table);
    }

    public static function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->columns(1)
            ->schema([
                Tabs::make('Profile')
                    ->tabs([
                        Tabs\Tab::make('Driver Information')
                            ->schema([
                                InfolistSection::make('Personal Informations')
                                    ->schema([
                                        Split::make([
                                            InfoListGrid::make(3)
                                                ->schema([
                                                    Group::make([
                                                        ImageEntry::make('user.cover_picture')
                                                            ->defaultImageUrl(url('/images/avatar.png'))
                                                            ->circular()
                                                            ->size(200)
                                                            ->label('Profile photo'),
                                                    ])->columnSpan(1),
                                                    Group::make([
                                                        TextEntry::make('user.name')
                                                            ->label('First Name')
                                                            ->getStateUsing(function ($record) {
                                                                return $record->user->name ?: 'No first name provided';
                                                            }),
                                                        TextEntry::make('user.last_name')
                                                            ->label('Last Name')
                                                            ->getStateUsing(function ($record) {
                                                                return $record->user->last_name ?: 'No full  last name provided';
                                                            }),

                                                        TextEntry::make('user.gender')
                                                            ->label('Gender')
                                                            ->badge()
                                                            ->icon('heroicon-s-user-circle')
                                                            ->getStateUsing(function ($record) {
                                                                if (! $record->user->gender) {
                                                                    return 'No gender provided';
                                                                }

                                                                return $record->user->gender->value === 'male' ? 'ذكر' : 'أنثى';
                                                            }),
                                                        TextEntry::make('id_number')
                                                            ->label('ID Number')
                                                            ->icon('heroicon-o-identification')
                                                            ->color('primary'),

                                                        TextEntry::make('id')
                                                            ->label('Driver ID')
                                                            ->icon('heroicon-o-identification')
                                                            ->color('primary')
                                                            ->visible(fn ($record) => in_array($record->global_status, [DriverGlobalStatus::blocked, DriverGlobalStatus::active])),
                                                        PhoneEntry::make('user.phone_number')->displayFormat(PhoneInputNumberType::INTERNATIONAL)
                                                            ->label('Phone Number')
                                                            ->icon('heroicon-o-device-phone-mobile'),

                                                        PhoneEntry::make('user.old_phone_number')
                                                            ->displayFormat(PhoneInputNumberType::INTERNATIONAL)
                                                            ->label('Old Phone Number')
                                                            ->icon('heroicon-o-phone-x-mark')
                                                            ->color('gray')
                                                            ->getStateUsing(function ($record) {
                                                                return $record->user->old_phone_number ?: 'No Previous Phone Number provided';
                                                            }),

                                                        TextEntry::make('user.email')
                                                            ->label('Email Address')
                                                            ->icon('heroicon-o-envelope')
                                                            ->color('primary')
                                                            ->getStateUsing(function ($record) {
                                                                return $record->user->email
                                                                    ? $record->user->email
                                                                    : 'No email provided';
                                                            }),

                                                        TextEntry::make('user.phone_verified_at')
                                                            ->label('Registration Date')
                                                            ->badge()
                                                            ->icon('heroicon-s-calendar-days')
                                                            ->getStateUsing(function ($record) {
                                                                return $record->user->created_at
                                                                    ? Carbon::parse($record->user->created_at)->format('Y-m-d')
                                                                    : ' ';
                                                            }),
                                                        TextEntry::make('user.address.address')
                                                            ->label('Address')
                                                            ->icon('heroicon-o-map-pin')
                                                            ->getStateUsing(function ($record) {
                                                                return $record->user->address()->orderBy('created_at', 'asc')->first()?->address
                                                                ?: 'No address provided';
                                                            })
                                                            ->prose(),

                                                        TextEntry::make('global_status')
                                                            ->label('Account status')
                                                            ->default('No status available')
                                                            ->badge(),

                                                    ])->columnSpan(2)->columns(2),
                                                ]),
                                        ])->from('lg'),
                                    ]),

                                InfolistSection::make('Blocking Reason')
                                    ->schema([
                                        TextEntry::make('user.blocking_reason')
                                            ->label(''),
                                    ])->visible(fn ($record) => $record->global_status === DriverGlobalStatus::blocked)
                                    ->collapsible(),

                                InfolistSection::make('Driver Performance')
                                    ->description('Performance Metrics')
                                    ->schema([
                                        InfolistRatingStar::make('average_driver_rating')
                                            ->default('No rating data available')
                                            ->label('Ratings')
                                            ->size('md'),
                                        TextEntry::make('Earnings')
                                            ->label('Earnings')
                                            ->default('No earnings data available')
                                            ->color('gray')
                                            ->icon('heroicon-o-currency-dollar'),

                                        TextEntry::make('Trips_number')
                                            ->label('Number of trips')
                                            ->default('No trips recorded')
                                            ->color('gray')
                                            ->icon('heroicon-o-truck'),

                                        TextEntry::make('Cancellation rate')
                                            ->label('Cancellation Rate')
                                            ->default('No cancellation data available')
                                            ->color('gray')
                                            ->icon('heroicon-o-x-circle'),
                                    ])
                                    ->columns(2)
                                    ->collapsible()
                                    ->visible(fn ($record) => in_array($record->global_status, [DriverGlobalStatus::blocked, DriverGlobalStatus::active])),

                                InfolistSection::make('Documents')
                                    ->description('Official Documentation')
                                    ->schema([
                                        TextEntry::make('documents.document_type')
                                            ->label('Document Type')
                                            ->hidden()
                                            ->formatStateUsing(function ($state) {
                                                $types = [
                                                    'personal_id_number' => 'Personal ID',
                                                    'passport' => 'Passport',
                                                    'national_id_number' => 'National ID',
                                                ];

                                                return $types[$state] ?? 'Unknown Document Type'; // Fallback for unexpected values
                                            }),

                                        TextEntry::make('documents.document_front')
                                            ->default('')
                                            ->label(function ($state, $record) {
                                                $type = $record->documents->document_type ?? 'Document';
                                                $labels = [
                                                    'personal_id_number' => 'Personal ID Front',
                                                    'passport' => 'Passport Front',
                                                    'national_id_number' => 'National ID Front',
                                                ];

                                                return $labels[$type] ?? 'Document Front';
                                            })
                                            ->formatStateUsing(function ($state, $record) {
                                                $url = $record->documents?->document_front ? asset('storage/'.$record->documents->document_front) : null;

                                                return $url ? "<a href='{$url}' target='_blank' class='text-blue-600 hover:text-blue-800 underline'>View Document Front</a>" : 'No Document Front Available';
                                            })
                                            ->html(),

                                        TextEntry::make('documents.document_back')
                                            ->default('')
                                            ->label(function ($state, $record) {
                                                $type = $record->documents->document_type ?? 'Document';
                                                $labels = [
                                                    'personal_id_number' => 'Personal ID Back',
                                                    'passport' => 'Passport Back',
                                                    'national_id_number' => 'National ID Back',
                                                ];

                                                return $labels[$type] ?? 'Document Back';
                                            })
                                            ->default('')
                                            ->formatStateUsing(function ($state, $record) {
                                                $url = $record->documents?->document_back ? asset('storage/'.$record->documents->document_back) : null;

                                                return $url ? "<a href='{$url}' target='_blank' class='text-blue-600 hover:text-blue-800 underline'>View Document Back</a>" : 'No Document Back Available';
                                            })
                                            ->html(),

                                        TextEntry::make('documents.license_front')
                                            ->label('Driving License Front')
                                            ->default('')
                                            ->formatStateUsing(function ($state, $record) {
                                                $url = $record->documents?->license_front ? asset('storage/'.$record->documents->license_front) : null;

                                                return $url ? "<a href='{$url}' target='_blank' class='text-blue-600 hover:text-blue-800 underline'>View License Front</a>" : 'No License Front Available';
                                            })
                                            ->html(),

                                        TextEntry::make('documents.license_back')
                                            ->label('Driving License Back')
                                            ->default('')
                                            ->formatStateUsing(function ($state, $record) {
                                                $url = $record->documents?->license_back ? asset('storage/'.$record->documents->license_back) : null;

                                                return $url ? "<a href='{$url}' target='_blank' class='text-blue-600 hover:text-blue-800 underline'>View License Back</a>" : 'No License Back Available';
                                            })
                                            ->html(),

                                        TextEntry::make('documents.license_expiry')
                                            ->label('License Expiry')
                                            ->date()
                                            ->color(fn ($state) => now()->parse($state)->isPast() ? 'danger' : 'success')
                                            ->icon('heroicon-o-calendar'),
                                    ])
                                    ->columns(2)
                                    ->collapsible(),

                                InfolistSection::make('Registration Details')
                                    ->description('System Registration Information')
                                    ->schema([
                                        TextEntry::make('user.phone_verified_at')
                                            ->label('Registration Date')
                                            ->badge()
                                            ->icon('heroicon-s-calendar-days')
                                            ->getStateUsing(function ($record) {
                                                return $record->user->created_at
                                                    ? Carbon::parse($record->user->created_at)->format('Y-m-d')
                                                    : ' ';
                                            }),
                                        TextEntry::make('user.phone_verified_at')
                                            ->label('Last Updated')
                                            ->badge()
                                            ->icon('heroicon-o-arrow-path')
                                            ->getStateUsing(function ($record) {
                                                return $record->user->updated_at
                                                    ? Carbon::parse($record->user->updated_at)->format('Y-m-d')
                                                    : ' ';
                                            }),
                                    ])
                                    ->visible(fn ($record) => in_array($record->global_status, [DriverGlobalStatus::blocked, DriverGlobalStatus::active]))
                                    ->columns(2)
                                    ->collapsible(),

                                InfolistSection::make('Rejected Fields/Documents')
                                    ->description('The following fields/documents must be updated by the driver after rejection.')
                                    ->schema([
                                        TextEntry::make('rejection_reason_columns')
                                            ->label('')
                                            ->formatStateUsing(function ($state) {
                                                if (empty($state)) {
                                                    return 'No rejection details available';
                                                }

                                                // Decode or parse the state
                                                if (is_string($state)) {
                                                    $rejectedFields = str_starts_with($state, '[') || str_starts_with($state, '{')
                                                        ? json_decode($state, true) ?? []
                                                        : explode(',', $state);
                                                } elseif (is_array($state)) {
                                                    $rejectedFields = $state;
                                                } else {
                                                    $rejectedFields = [];
                                                }

                                                $rejectedFields = array_map('trim', $rejectedFields);

                                                // Define display names
                                                $fieldDisplayNames = [
                                                    // Personal Info
                                                    'name' => 'First Name',
                                                    'last_name' => 'Last Name',
                                                    'address' => 'Address',
                                                    'gender' => 'Gender',
                                                    'id_number' => 'ID Number',

                                                    // Documents
                                                    'document_type' => 'ID Type',
                                                    'document_front' => 'ID Front',
                                                    'document_back' => 'ID Back',
                                                    'license_front' => 'License Front',
                                                    'license_back' => 'License Back',
                                                    'license_expiry' => 'License Expiry',

                                                    // Vehicle
                                                    'vehicle_photo' => 'Vehicle Photo',
                                                    'vehicle_type' => 'Vehicle Type',
                                                    'license_plate_number' => 'License Plate Number',
                                                    'vehicle_model' => 'Vehicle Model',
                                                    'vehicle_brand' => 'Vehicle Brand',
                                                    'year' => 'Year',
                                                    'vehicle_color' => 'Vehicle Color',
                                                    'seats_number' => 'Seats Number',
                                                    'is_covered' => 'Is Covered',
                                                    'category' => 'Vehicle Category',
                                                    'weight_category' => 'Weight Category',

                                                    // Legal
                                                    'insurance' => 'Insurance',
                                                    'technical_inspection' => 'Technical Inspection',
                                                    'roaming_permit' => 'Roaming Permit',
                                                    'insurance_expiry' => 'Insurance Expiry',
                                                    'technical_inspection_expiry' => 'Technical Inspection Expiry',
                                                    'roaming_permit_expiry' => 'Roaming Permit Expiry',
                                                ];

                                                // Categorized structure
                                                $categories = [
                                                    'Personal Information' => ['name', 'last_name', 'address', 'gender', 'id_number'],
                                                    'Documents' => ['document_type', 'document_front', 'document_back', 'license_front', 'license_back', 'license_expiry'],
                                                    'Vehicle Details' => [
                                                        'vehicle_photo', 'vehicle_type', 'license_plate_number', 'vehicle_model',
                                                        'vehicle_brand', 'year', 'vehicle_color', 'seats_number',
                                                        'is_covered', 'category', 'weight_category',
                                                    ],
                                                    'Legal & Permits' => [
                                                        'insurance', 'technical_inspection', 'roaming_permit',
                                                        'insurance_expiry', 'technical_inspection_expiry', 'roaming_permit_expiry',
                                                    ],
                                                ];

                                                // Build the HTML output
                                                $output = '<div class="space-y-4">';

                                                foreach ($categories as $category => $fields) {
                                                    $rejectedInCategory = array_intersect($fields, $rejectedFields);

                                                    if (! empty($rejectedInCategory)) {
                                                        $output .= "<div><strong class='text-gray-800 dark:text-gray-200'>{$category}:</strong><ul class='list-disc pl-5 text-gray-700 dark:text-gray-300 mt-1'>";
                                                        foreach ($rejectedInCategory as $field) {
                                                            $label = $fieldDisplayNames[$field] ?? ucwords(str_replace('_', ' ', $field));
                                                            $output .= "<li><span class='text-red-600 font-medium'>{$label}</span></li>";
                                                        }
                                                        $output .= '</ul></div>';
                                                    }
                                                }

                                                if ($output === '<div class="space-y-4">') {
                                                    $output .= '<p>No specific fields were marked for rejection.</p>';
                                                }

                                                $output .= '</div>';

                                                return $output;
                                            })
                                            ->html(),
                                        // Allow HTML rendering
                                    ])
                                    ->visible(fn ($record) => $record->previous_global_status === DriverGlobalStatus::rejected->value ||
    $record->global_status === DriverGlobalStatus::rejected
                                    )
                                    ->columns(1)
                                    ->collapsible(),
                            ]),

                        Tabs\Tab::make('Vehicle Information')
                            ->schema(
                                function ($record) {
                                    return [
                                        \Filament\Infolists\Components\Livewire::make(Vehicles::class, ['driver' => $record])->key(Str::random()),

                                    ];
                                }
                            ),

                        Tabs\Tab::make('Trips History')
                            ->schema(
                                function ($record) {
                                    return [
                                        \Filament\Infolists\Components\Livewire::make(Trips::class, ['driver' => $record])->key(Str::random()),

                                    ];
                                }
                            )->visible(fn ($record) => in_array($record->global_status, [DriverGlobalStatus::blocked, DriverGlobalStatus::deleted, DriverGlobalStatus::active])),
                    ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            // RelationManagers\TripsRelationManager::class,
            // RelationManagers\DriverAvailabilitiesRelationManager::class,
            RelationManagers\VehiclesRelationManager::class,
            // RelationManagers\VehicleEquipmentsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListDrivers::route('/'),
            'create' => Pages\CreateDriver::route('/create'),
            'view' => Pages\ViewDriver::route('/{record}'),
            'edit' => Pages\EditDriver::route('/{record}/edit'),
            'view_vehicle' => Pages\ViewDriverVehicle::route('/{record}/vehicle/{vehicleId}'),
            'view_trip' => Pages\ViewDriverTrip::route('/{record}/trip/{tripId}'),
        ];
    }
}
