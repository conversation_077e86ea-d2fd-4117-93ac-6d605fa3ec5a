<?php

namespace App\Filament\Resources\Panel;

use App\Filament\Resources\Panel\AreaResource\Pages;
use App\Forms\Components\PolygonMapField;
use App\Livewire\PolygonMap;
use App\Models\Area;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Form;
use Filament\Infolists\Components\Grid;
use Filament\Infolists\Components\IconEntry;
use Filament\Infolists\Components\Livewire;
use Filament\Infolists\Components\Tabs as infolistTab;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Infolist;
use Filament\Notifications\Notification;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\ToggleColumn;
use Filament\Tables\Table;

class AreaResource extends Resource
{
    protected static ?string $model = Area::class;

    protected static ?string $navigationIcon = 'mdi-map-search';

    protected static ?string $navigationGroup = 'Configuration';

    protected static ?int $navigationSort = 0;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Area Information')
                    ->description('Enter the area details in both Arabic and English.')
                    ->icon('heroicon-o-map')
                    ->schema([
                        TextInput::make('name_ar')
                            ->label('Area Name (Arabic)')
                            ->placeholder('أدخل اسم المنطقة باللغة العربية')
                            ->required()
                            ->unique('areas', 'name_ar', ignoreRecord: true)
                            ->string()
                            ->regex('/^(?!.*[\x{0660}-\x{0669}\x{06F0}-\x{06F9}\-0-9])[\p{Arabic} ]+$/u')
                            ->validationMessages([
                                'regex' => 'The area name (Arabic) could contain only Arabic letters, with spaces between words allowed.',
                            ])
                            ->maxLength(30)
                            ->live(debounce: 300)
                            ->afterStateUpdated(function (?string $state, callable $set) {
                                if ($state) {
                                    // Trim leading and trailing spaces
                                    $trimmedState = trim($state);
                                    // Replace multiple consecutive spaces with single space
                                    $cleanedState = preg_replace('/\s{2,}/', ' ', $trimmedState);

                                    // Only update if the cleaned state is different from current state
                                    if ($cleanedState !== $state) {
                                        $set('name_ar', $cleanedState);
                                    }
                                }
                            })
                            ->dehydrateStateUsing(fn (?string $state): ?string => $state ? trim($state) : null)
                            ->suffixIcon('mdi-translate')
                            ->helperText('Enter the area name in Arabic. Only Arabic letters and spaces are allowed.'),
                        //
                        TextInput::make('name_en')
                            ->label('Area Name (English)')
                            ->placeholder('Enter area name in English')
                            ->required()
                            ->string()
                            ->unique('areas', 'name_en', ignoreRecord: true)
                            ->regex('/^[A-Za-z]+( [A-Za-z]+)*$/')
                            ->validationMessages([
                                'regex' => 'The area name (English) could contain only English letters, with spaces between words allowed.',
                            ])
                            ->maxLength(30)
                            ->live(debounce: 300)
                            ->afterStateUpdated(function (?string $state, callable $set) {
                                if ($state) {
                                    // Trim leading and trailing spaces
                                    $trimmedState = trim($state);
                                    // Replace multiple consecutive spaces with single space
                                    $cleanedState = preg_replace('/\s{2,}/', ' ', $trimmedState);
                                    // Capitalize first letter
                                    $finalState = ucfirst($cleanedState);

                                    // Only update if the final state is different from current state
                                    if ($finalState !== $state) {
                                        $set('name_en', $finalState);
                                    }
                                }
                            })
                            ->dehydrateStateUsing(fn (?string $state): ?string => $state ? trim($state) : null)
                            ->suffixIcon('mdi-translate')
                            ->helperText('Enter the area name in English. Only English letters and spaces are allowed.'),
                    ])
                    ->columns(2),

                Section::make('Status')
                    ->description('Set the active status of the area.')
                    ->icon('heroicon-m-check')
                    ->schema([
                        Toggle::make('is_active')
                            ->label('Active Status')
                            ->rules(['boolean'])
                            ->onIcon('heroicon-m-check')
                            ->offIcon('heroicon-m-x-mark')
                            ->inline()
                            ->default(true)
                            ->helperText('Toggle to activate or deactivate the area.')
                            ->columnSpanFull(),
                    ]),

                Section::make('Area Boundary')
                    ->description('Draw the boundary of the area on the map.')
                    ->icon('mdi-shape-polygon-plus')
                    ->schema([
                        PolygonMapField::make('polygon')
                            ->label('Area Boundary')
                            ->helperText('Draw a polygon to define the area boundary. The polygon should not overlap with existing areas.')
                            ->required()
                            ->existingPolygons(function ($livewire) {
                                $query = Area::query();
                                if (isset($livewire->record) && $livewire->record->exists) {
                                    $query->where('id', '!=', $livewire->record->id);
                                }

                                return $query->pluck('polygon')
                                    ->filter()
                                    ->map(fn ($polygon) => is_string($polygon) ? json_decode($polygon, true) : $polygon)
                                    ->toArray();
                            })
                            ->columnSpanFull(),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('id')
                    ->label('ID')
                    ->searchable(),

                TextColumn::make('name_ar')
                    ->label('Area Name (Arabic)')
                    ->searchable(),

                TextColumn::make('name_en')
                    ->label('Area Name (English)')
                    ->searchable(),

                ToggleColumn::make('is_active')
                    ->label('Status')
                    ->afterStateUpdated(function ($state, $record) {
                        $record->is_active = $state;
                        $record->save();

                        Notification::make()
                            ->title($state ? 'Activated' : 'Deactivated')
                            ->body('The area has been '.($state ? 'activated' : 'deactivated').'.')
                            ->success()
                            ->send();
                    }),
            ])->defaultSort('created_at', 'desc')
            ->paginationPageOptions([5, 10, 25, 50])

            ->filters([])
            ->actions([
                Tables\Actions\ActionGroup::make([
                    Tables\Actions\EditAction::make()
                        ->successNotification(
                            Notification::make()
                                ->success()
                                ->title('Area updated successfully')
                        ),
                    Tables\Actions\ViewAction::make(),
                    Tables\Actions\DeleteAction::make()
                        ->successNotification(
                            Notification::make()
                                ->success()
                                ->title('Area deleted successfully')
                        ),
                ]),
            ]);

    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListAreas::route('/'),
            'create' => Pages\CreateArea::route('/create'),
            'edit' => Pages\EditArea::route('/{record}/edit'),
            'view' => Pages\ViewArea::route('/{record}'),
        ];
    }

    public static function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                infolistTab::make('Tabs')
                    ->tabs([
                        infolistTab\Tab::make('Details')
                            ->schema([
                                Grid::make(3)
                                    ->schema([
                                        TextEntry::make('name_ar')
                                            ->label('Area Name (Arabic)'),

                                        TextEntry::make('name_en')
                                            ->label('Area Name (English)'),

                                        IconEntry::make('is_active')
                                            ->label('Active Status')
                                            ->size(IconEntry\IconEntrySize::TwoExtraLarge)
                                            ->boolean(),

                                        Livewire::make(PolygonMap::class, [
                                            'polygon' => $infolist->record->polygon ?? '[]',
                                        ])->columnSpanFull()->extraAttributes(['style' => 'height: 600px;']),
                                    ]),

                            ]),
                        infolistTab\Tab::make('Pricing')
                            ->schema([
                                Grid::make(2)
                                    ->schema([
                                        TextEntry::make('base_fare_adjustment_type')
                                            ->badge()
                                            ->label('Base Fare Type')
                                            ->getStateUsing(fn ($record) => $record->base_fare_adjustment_type ?: '-'),

                                        TextEntry::make('distance_fare_adjustment_type')
                                            ->badge()
                                            ->label('Distance Fare Type')
                                            ->getStateUsing(fn ($record) => $record->distance_fare_adjustment_type ?: '-'),

                                        TextEntry::make('base_fare')
                                            ->numeric()
                                            ->label('Base Fare Adjustment')
                                            ->default(0)
                                            ->visible(fn ($record) => $record->base_fare_adjustment_type === null),

                                        TextEntry::make('base_fare')
                                            ->label('Base Fare Adjustment')
                                            ->default(0)
                                            ->suffix(fn ($record) => $record->base_fare_adjustment_type && $record->base_fare_adjustment_type->name === 'fixed' ? ' LYD' : null)
                                            ->visible(fn ($record) => $record->base_fare_adjustment_type && $record->base_fare_adjustment_type->name === 'fixed'),

                                        TextEntry::make('base_fare_adjustment')
                                            ->label('Base Fare Adjustment')
                                            ->default(0)
                                            ->suffix(fn ($record) => $record->base_fare_adjustment_type && $record->base_fare_adjustment_type->name === 'percentage' ? ' %' : null)
                                            ->visible(fn ($record) => $record->base_fare_adjustment_type && $record->base_fare_adjustment_type->name === 'percentage'),

                                        TextEntry::make('distance_fare')
                                            ->numeric()
                                            ->label('Distance Fare Adjustment')
                                            ->default(0)
                                            ->visible(fn ($record) => $record->distance_fare_adjustment_type === null),

                                        TextEntry::make('distance_fare')
                                            ->label('Distance Fare Adjustment')
                                            ->default(0)
                                            ->suffix(fn ($record) => $record->distance_fare_adjustment_type && $record->distance_fare_adjustment_type->name === 'fixed' ? ' LYD' : null)
                                            ->visible(fn ($record) => $record->distance_fare_adjustment_type && $record->distance_fare_adjustment_type->name === 'fixed'),

                                        TextEntry::make('distance_fare_adjustment')
                                            ->label('Distance Fare Adjustment')
                                            ->default(0)
                                            ->suffix(fn ($record) => $record->distance_fare_adjustment_type && $record->distance_fare_adjustment_type->name === 'percentage' ? ' %' : null)
                                            ->visible(fn ($record) => $record->distance_fare_adjustment_type && $record->distance_fare_adjustment_type->name === 'percentage'),
                                    ]),
                            ]),
                    ])->columnSpanFull(),

            ]);
    }
}
