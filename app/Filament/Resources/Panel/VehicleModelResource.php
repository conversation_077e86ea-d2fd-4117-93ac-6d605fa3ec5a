<?php

namespace App\Filament\Resources\Panel;

use App\Filament\Resources\Panel\VehicleModelResource\Pages;
use App\Models\VehicleModel;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Filament\Infolists\Components;
use Filament\Infolists\Components\IconEntry;
use Filament\Infolists\Infolist;
use Filament\Notifications\Notification;
use Filament\Resources\Resource;
use Filament\Support\Enums\IconPosition;
use Filament\Tables;
use Filament\Tables\Actions\Action;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\ToggleColumn;
use Filament\Tables\Grouping\Group;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class VehicleModelResource extends Resource
{
    protected static ?string $model = VehicleModel::class;

    protected static ?string $navigationIcon = 'mdi-car-multiple';

    protected static ?int $navigationSort = 3;

    protected static ?string $navigationGroup = 'Configuration';

    // Cached regex patterns to compile them once
    private const ARABIC_NAME_REGEX = '/^[\p{Arabic}0-9]+(-[\p{Arabic}0-9]+)*( [\p{Arabic}0-9]+(-[\p{Arabic}0-9]+)*)*$/u';

    private const ENGLISH_NAME_REGEX = '/^[A-Za-z0-9ŠšÉéÈèÊêËë\-]+( [A-Za-z0-9ŠšÉéÈèÊêËë\-]+)*$/';

    public static function getModelLabel(): string
    {
        return __('crud.vehicleModels.itemTitle');
    }

    public static function getPluralModelLabel(): string
    {
        return __('crud.vehicleModels.collectionTitle');
    }

    public static function getNavigationLabel(): string
    {
        return __('crud.vehicleModels.collectionTitle');
    }

    /**
     * Reusable method to validate a field dynamically.
     */
    private static function validateField(HasForms $livewire, TextInput $component, string $regex, string $message): void
    {
        $livewire->validateOnly($component->getStatePath(), [
            $component->getStatePath() => [
                'required',
                "regex:{$regex}",
            ],
        ], [
            $component->getStatePath().'.regex' => $message,
        ]);
    }

    public static function form(Form $form): Form
    {
        return $form->schema([

            Select::make('vehicle_brand_id')
                ->label('Brand Name (Arabic)')
                ->required()
                ->relationship(
                    'vehicleBrand',
                    'name_ar', // Shows the Arabic name of the brand
                    fn ($query) => $query->where('status', true) // Only show active brands in the dropdown
                )
                ->searchable()
                ->placeholder('Select vehicle brand')
                ->preload()
                ->native(false)
                ->columnSpanFull()
                ->getOptionLabelUsing(function ($record) {
                    return $record->vehicleBrand->name_ar; // Show the Arabic name of the brand in the dropdown
                })
                ->afterStateUpdated(function ($set, $state) {
                    // Check if the selected brand is deactivated
                    $brand = \App\Models\VehicleBrand::find($state);

                    // If the brand is deactivated
                    if ($brand && ! $brand->status) {
                        // Show a warning if the user is trying to select a deactivated brand
                        Notification::make()
                            ->title('Warning')
                            ->body('You are selecting a deactivated brand. Please ensure this is intentional.')
                            ->warning()
                            ->send();

                        // Optionally, keep the previous brand ID if the brand is deactivated
                        $set('vehicle_brand_id', $state); // Keep selected brand ID, but notify user
                    }
                })
                ->helperText(function ($record) {
                    if (! $record) {
                        return ''; // Return empty string if creating a new record
                    }

                    $brand = \App\Models\VehicleBrand::find($record->vehicle_brand_id);

                    if ($brand && ! $brand->status) {
                        return 'The selected brand is deactivated. You can change it, but make sure to select an active brand.';
                    }

                    return '';
                }),

            // Arabic Name Field
            TextInput::make('name_ar')
                ->label('Model Name (Arabic)')
                ->rules(['required'])
                ->markAsRequired()
                ->maxLength(30)
                ->placeholder('أدخل اسم الطراز باللغة العربية')
                ->autofocus()
                ->columnSpanFull()
                ->afterStateUpdated(fn ($set, $state) => $set('name_ar', preg_replace('/\s+/', ' ', trim($state))))
                ->regex(self::ARABIC_NAME_REGEX)
                ->validationMessages([
                    'regex' => 'The model name (Arabic) could contain only Arabic letters and numbers, with spaces between words allowed.',
                ])
                ->unique('vehicle_models', 'name_ar', ignoreRecord: true)
                ->live(debounce: 500)
                ->afterStateUpdated(fn (HasForms $livewire, TextInput $component) => self::validateField($livewire, $component, self::ARABIC_NAME_REGEX, 'The model name (Arabic) could contain only Arabic letters and numbers, with spaces between words allowed.')),

            // English Name Field
            TextInput::make('name_en')
                ->label('Model Name (English)')
                ->rules(['required'])
                ->markAsRequired()
                ->maxLength(30)
                ->columnSpanFull()
                ->placeholder('Enter vehicle model name')
                ->autofocus()
                ->regex(self::ENGLISH_NAME_REGEX)
                ->afterStateUpdated(fn ($set, $state) => $set('name_en', preg_replace('/\s+/', ' ', trim(ucfirst($state)))))
                ->validationMessages([
                    'regex' => 'The model name (English) could contain only English letters, numbers, special characters, with spaces between words.',
                ])
                ->unique('vehicle_models', 'name_en', ignoreRecord: true)
                ->live(debounce: 500)
                ->afterStateUpdated(fn (HasForms $livewire, TextInput $component) => self::validateField($livewire, $component, self::ENGLISH_NAME_REGEX, 'The model name (English) could contain only English letters, numbers, special characters, with spaces between words.')),

            Toggle::make('status')
                ->required()
                ->rules(['boolean'])
                ->default(true)
                ->onIcon('heroicon-m-check')
                ->offIcon('heroicon-m-x-mark')
                ->inline()
                ->helperText(function ($record) {
                    if ($record && $record->exists && $record->vehicles()->exists()) {
                        return 'This model is associated with one or more vehicles and cannot be deactivated.';
                    }

                    if ($record && $record->exists && $record->vehicleBrand->status === false) {
                        return 'This model belongs to a deactivated brand and cannot be activated.';
                    }

                    return '';
                })
                ->dehydrated(function ($state, $record) {
                    if ($record && $record->exists) {
                        // Prevent deactivation if associated with vehicles
                        if ($record->vehicles()->exists() && $state === false) {
                            return true;
                        }

                        // Prevent activation if the associated brand is inactive
                        if ($record->vehicleBrand->status === false && $state === true) {
                            return false;
                        }
                    }

                    return $state;
                })
                ->disabled(function ($record) {
                    // Disable toggle if model is linked to vehicles OR brand is deactivated
                    return $record && $record->exists && ($record->vehicles()->exists() || $record->vehicleBrand->status === false);
                }),
        ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->poll('60s')
            ->groups([
                Group::make('vehicleBrand.name_ar')
                    ->getTitleFromRecordUsing(function ($record) {
                        return $record->vehicleBrand->name_ar.'  -  '.$record->vehicleBrand->name_en;
                    })
                    ->groupQueryUsing(fn (Builder $query) => $query
                        ->groupBy('vehicle_brands.id', 'vehicle_brands.name_ar', 'vehicle_brands.name_en')
                        ->orderByDesc('latest_model_created_at')
                    )
                    ->label(''),
            ])
            ->defaultGroup('vehicleBrand.name_ar')
            ->groupingSettingsHidden()
            ->modifyQueryUsing(function (Builder $query) {
                $query
                    ->join('vehicle_brands', 'vehicle_models.vehicle_brand_id', '=', 'vehicle_brands.id')
                    ->orderByDesc('latest_model_created_at')
                    ->orderByDesc('vehicle_models.created_at')
                    ->select('vehicle_models.*', 'vehicle_brands.name_ar as brand_name_ar', 'vehicle_brands.name_en as brand_name_en')
                    ->addSelect([
                        'latest_model_created_at' => VehicleModel::select('created_at')
                            ->whereColumn('vehicle_brand_id', 'vehicle_brands.id')
                            ->latest('created_at')
                            ->limit(1),
                    ]);
            })
            ->columns([
                TextColumn::make('name_ar')
                    ->label('Model Name (Arabic)')
                    ->searchable([
                        'vehicle_models.name_ar',
                        'vehicle_brands.name_ar',
                        'vehicle_brands.name_en',
                    ]),

                TextColumn::make('name_en')
                    ->label('Model Name (English)')
                    ->searchable([
                        'vehicle_models.name_en',
                        'vehicle_brands.name_ar',
                        'vehicle_brands.name_en',
                    ]),

                // TextColumn::make('brand_name_ar')
                //     ->label('Brand Name (Arabic)')
                //     ->searchable(),

                // TextColumn::make('brand_name_en')
                //     ->label('Brand Name (English)')
                //     ->searchable(),

                ToggleColumn::make('status')
                    ->label('Status')
                    ->afterStateUpdated(function ($state, $record) {
                        if ($state === true && $record->vehicleBrand->status === false) {
                            Notification::make()
                                ->title('Activation Prevented')
                                ->body('This model cannot be activated because its associated brand is deactivated.')
                                ->warning()
                                ->send();

                            $record->status = false;
                            $record->save();

                            return;
                        }

                        if ($state === false && $record->vehicles()->exists()) {
                            Notification::make()
                                ->title('Deactivation Prevented')
                                ->body('This model cannot be deactivated because it is assigned to one or more vehicles.')
                                ->warning()
                                ->send();

                            $record->status = true;
                            $record->save();
                        } else {
                            $record->status = $state;
                            $record->save();

                            Notification::make()
                                ->title($state ? 'Activated' : 'Deactivated')
                                ->body('The vehicle model has been '.($state ? 'activated' : 'deactivated').'.')
                                ->success()
                                ->send();
                        }
                    }),

            ])

            ->filters([])
            ->filtersTriggerAction(
                fn (Action $action) => $action
                    ->slideOver()
                    ->modalIcon('heroicon-o-funnel')
                    ->button()
                    ->size('md')
                    ->icon('heroicon-o-funnel')
                    ->label('Filter'),
            )
            ->actions([
                Tables\Actions\ActionGroup::make([
                    Tables\Actions\EditAction::make()
                        ->successNotification(
                            Notification::make()
                                ->success()
                                ->title('Vehicle model updated successfully')
                        ),
                    Tables\Actions\ViewAction::make(),
                    Tables\Actions\DeleteAction::make()
                        ->successNotification(
                            Notification::make()
                                ->success()
                                ->title('Vehicle model deleted successfully')
                        )
                        ->before(function ($record, Tables\Actions\DeleteAction $action) {
                            if ($record->vehicles()->exists()) {
                                Notification::make()
                                    ->title('Deletion Prevented')
                                    ->body('This model cannot be deleted because it is assigned to one or more vehicles.')
                                    ->warning()
                                    ->send();

                                $action->cancel();
                            }
                        }),
                ]),
            ])
            ->defaultSort('created_at', 'desc')
            ->bulkActions([])
            ->paginationPageOptions([5, 10, 25, 50]);
    }

    public static function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Components\Section::make('Vehicle Model Details')
                    ->schema([
                        Components\Section::make('Brand')
                            ->schema([
                                Components\TextEntry::make('vehicleBrand.name_ar')
                                    ->label('Brand Name (Arabic)')
                                    ->icon('heroicon-o-truck')
                                    ->iconPosition(IconPosition::Before)
                                    ->badge(fn (): string => 'Arabic Name')
                                    ->columnSpan(1),

                                Components\TextEntry::make('vehicleBrand.name_en')
                                    ->label('Brand Name (English)')
                                    ->icon('heroicon-o-truck')
                                    ->iconPosition(IconPosition::Before)
                                    ->badge(fn (): string => 'English Name')
                                    ->columnSpan(1),

                                IconEntry::make('vehicleBrand.status')
                                    ->label('Brand Status')
                                    ->size(IconEntry\IconEntrySize::TwoExtraLarge)
                                    ->boolean(),

                            ])->inlineLabel(),
                        Components\Section::make('Model')
                            ->schema([
                                Components\TextEntry::make('name_ar')
                                    ->label('Model Name (Arabic)')
                                    ->icon('heroicon-o-truck')
                                    ->iconPosition(IconPosition::Before)
                                    ->badge(fn (): string => 'Arabic Name')
                                    ->columnSpan(1),

                                Components\TextEntry::make('name_en')
                                    ->label('Model Name (English)')
                                    ->icon('heroicon-o-truck')
                                    ->iconPosition(IconPosition::Before)
                                    ->badge(fn (): string => 'English Name')
                                    ->columnSpan(1),

                                IconEntry::make('status')
                                    ->label('Model Status')
                                    ->size(IconEntry\IconEntrySize::TwoExtraLarge)
                                    ->boolean(),
                            ])->inlineLabel(),
                    ])
                    ->columnSpanFull()
                    ->description('Overview of the vehicle model details.'),
            ]);
    }

    public static function getRelations(): array
    {
        return [];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListVehicleModels::route('/'),
            // 'view' => Pages\ViewVehicleModel::route('/{record}'),
            // 'edit' => Pages\EditVehicleModel::route('/{record}/edit'),
        ];
    }
}
