<?php

namespace App\Filament\Resources\Panel;

use App\Filament\Resources\Panel\VehicleEquipmentResource\Pages;
use App\Models\VehicleEquipment;
use Filament\Forms\Components\Fieldset;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Filament\Infolists\Components\Grid;
use Filament\Infolists\Components\IconEntry;
use Filament\Infolists\Components\Tabs as infolistTab;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Infolist;
use Filament\Notifications\Notification;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\ToggleColumn;
use Filament\Tables\Table;
use Guava\FilamentIconPicker\Forms\IconPicker;
use Guava\FilamentIconPicker\Tables\IconColumn;

class VehicleEquipmentResource extends Resource
{
    protected static ?string $model = VehicleEquipment::class;

    protected static ?string $navigationIcon = 'heroicon-o-squares-plus';

    public static ?string $label = 'Equipments';

    protected static ?string $navigationGroup = 'Configuration';

    protected static ?int $navigationSort = 4;

    // Cached regex patterns to compile them once
    private const ARABIC_NAME_REGEX = '/^(?!.*[\x{0660}-\x{0669}\x{06F0}-\x{06F9}\-0-9])[\p{Arabic} ]+$/u';

    private const ENGLISH_NAME_REGEX = '/^[A-Za-z]+( [A-Za-z]+)*$/';

    /**
     * Reusable method to validate a field dynamically.
     */
    private static function validateField(HasForms $livewire, TextInput $component, string $regex, string $message): void
    {
        $livewire->validateOnly($component->getStatePath(), [
            $component->getStatePath() => [
                'required',
                "regex:{$regex}",
            ],
        ], [
            $component->getStatePath().'.regex' => $message,
        ]);
    }

    public static function form(Form $form): Form
    {
        return $form->schema([
            Fieldset::make('Vehicle Equipment Details')
                ->schema([
                    Hidden::make('additional_fare')
                        ->default('0.00'),

                    TextInput::make('name_ar')
                        ->label('Equipment Name (Arabic)')
                        ->rules(['required'])
                        ->markAsRequired()
                        ->afterStateUpdated(fn ($set, $state) => $set('name_ar', preg_replace('/\s+/', ' ', trim($state))))
                        ->regex(self::ARABIC_NAME_REGEX)
                        ->maxLength(30)
                        ->unique(table: 'vehicle_equipment', column: 'name_ar', ignoreRecord: true)
                        ->validationMessages([
                            'regex' => 'The equipment Name (Arabic) could contain only Arabic letters, with spaces between words allowed.',
                        ])
                        ->columnSpanFull()
                        ->live(debounce: 500)
                        ->afterStateUpdated(fn (HasForms $livewire, TextInput $component) => self::validateField($livewire, $component, self::ARABIC_NAME_REGEX, 'The equipment Name (Arabic) could contain only Arabic letters, with spaces between words allowed.')),

                    // English Name Field
                    TextInput::make('name_en')
                        ->label('Equipment Name (English)')
                        ->rules(['required'])
                        ->markAsRequired()
                        ->afterStateUpdated(fn ($set, $state) => $set('name_en', preg_replace('/\s+/', ' ', trim(ucfirst($state)))))
                        ->regex(self::ENGLISH_NAME_REGEX)
                        ->maxLength(30)
                        ->validationMessages([
                            'regex' => 'The equipment Name (English) could contain only English letters, with spaces between words allowed.',
                        ])
                        ->unique(table: 'vehicle_equipment', column: 'name_en', ignoreRecord: true)
                        ->columnSpanFull()
                        ->live(debounce: 500)
                        ->afterStateUpdated(fn (HasForms $livewire, TextInput $component) => self::validateField($livewire, $component, self::ENGLISH_NAME_REGEX, 'The equipment Name (English) could contain only English letters, with spaces between words allowed.')),

                    IconPicker::make('icon')
                        ->label('Select Icon')
                        ->sets(null)
                        ->cacheable(false)
                        ->columns([
                            'default' => 1,
                            'sm' => 2,
                            'lg' => 3,
                            '2xl' => 5,
                        ])
                        ->placeholder('Click to select an icon')
                        ->reactive()
                        ->required(),

                    Toggle::make('status')
                        ->required()
                        ->rules(['boolean'])
                        ->default(true)
                        ->disabled(fn ($livewire) => $livewire->isFormDisabled() || $livewire->isSubmitting)
                        ->onIcon('heroicon-m-check')
                        ->offIcon('heroicon-m-x-mark')
                        ->inline()
                        ->helperText(function ($record) {
                            // Ensure $record exists and is not a new record
                            if ($record && $record->exists && $record->vehicles()->exists()) {
                                return 'This equipment is associated with one or more vehicles and cannot be desactivated.';
                            }

                            return '';
                        })
                        ->dehydrated(function ($state, $record) {
                            // Ensure $record exists and is not a new record
                            if ($record && $record->exists && $record->vehicles()->exists() && $state === false) {
                                return true; // Prevent deactivating if associated with vehicles
                            }

                            return $state;
                        })
                        ->disabled(function ($record) {
                            // Only disable the toggle when updating an existing record with associated vehicles
                            return $record && $record->exists && $record->vehicles()->exists();
                        }),
                ])->columns(1),
        ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name_ar')
                    ->label('Equipment Name (Arabic) ')
                    ->searchable(),

                Tables\Columns\TextColumn::make('name_en')
                    ->label('Equipment Name (English)')
                    ->searchable(),

                IconColumn::make('icon')
                    ->label('Icon'),

                ToggleColumn::make('status')
                    ->label('Status')
                    ->afterStateUpdated(function ($state, $record) {
                        if ($state === false && $record->vehicles()->exists()) {
                            // Send a notification if deactivation is attempted while vehicles are assigned
                            Notification::make()
                                ->title('Deactivation Prevented')
                                ->body('This equipment cannot be deactivated because it is assigned to one or more vehicles.')
                                ->warning()
                                ->send();

                            // Revert the status back to true if deactivation is prevented
                            $record->{'status'} = true;
                            $record->save();
                        } else {
                            // Update the status when activated or deactivated
                            $record->{'status'} = $state;
                            $record->save();

                            // Send notification for activation or deactivation
                            Notification::make()
                                ->title($state ? 'Activated' : 'Deactivated')
                                ->body('The equipment has been '.($state ? 'activated' : 'deactivated').'.')
                                ->success()
                                ->send();
                        }
                    }),
            ])

            ->actions([
                Tables\Actions\ActionGroup::make([
                    Tables\Actions\EditAction::make()
                        ->successNotification(
                            Notification::make()
                                ->success()
                                ->title('Equipment updated successfully')
                        ),
                    Tables\Actions\ViewAction::make(),
                    Tables\Actions\DeleteAction::make()
                        ->successNotification(
                            Notification::make()
                                ->success()
                                ->title('Equipment deleted successfully')
                        )
                        ->before(function ($record, Tables\Actions\DeleteAction $action) {
                            // Check if the VehicleEquipment is assigned to any Vehicles
                            if ($record->vehicles()->exists()) {
                                Notification::make()
                                    ->title('Deletion Prevented')
                                    ->body('This equipment cannot be deleted because it is assigned to one or more vehicles.')
                                    ->warning()
                                    ->send();
                                $action->cancel();
                            }
                        }),
                ]),
            ])

            ->defaultSort('created_at', 'desc')
            ->paginationPageOptions([5, 10, 25, 50]);
    }

    public static function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                infolistTab::make('Tabs')
                    ->tabs([
                        infolistTab\Tab::make('Details')
                            ->schema([
                                Grid::make(2)
                                    ->schema([
                                        TextEntry::make('name_ar')
                                            ->label('Equipment Name (Arabic)')
                                            ->icon('heroicon-o-squares-plus'),
                                        TextEntry::make('name_en')
                                            ->label('Equipment Name (English)')
                                            ->icon('heroicon-o-squares-plus'),
                                        IconEntry::make('icon')
                                            ->label('Equipment Icon')
                                            ->icon(function ($state) {
                                                return $state;
                                            })
                                            ->size(IconEntry\IconEntrySize::TwoExtraLarge),
                                        IconEntry::make('status')
                                            ->label('Equipment Status')
                                            ->size(IconEntry\IconEntrySize::TwoExtraLarge)
                                            ->boolean(),
                                    ]),
                            ]),
                        infolistTab\Tab::make('Pricing')
                            ->schema([
                                TextEntry::make('additional_fare')
                                    ->label('Additional Fare Adjustment')
                                    ->formatStateUsing(fn ($state) => number_format($state, 2).' LYD'),
                            ]),
                    ])->columnSpanFull(),
            ]);
    }

    public static function getRelations(): array
    {
        return [];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListVehicleEquipment::route('/'),
            // 'create' => Pages\CreateVehicleEquipment::route('/create'),
            // 'edit' => Pages\EditVehicleEquipment::route('/{record}/edit'),
        ];
    }
}
