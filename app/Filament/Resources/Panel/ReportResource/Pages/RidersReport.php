<?php

namespace App\Filament\Resources\Panel\ReportResource\Pages;

use App\Filament\Exports\RidersReportExporter;
use App\Filament\Resources\Panel\ReportResource;
use App\Models\Rider;
use Carbon\Carbon;
use Filament\Actions\ExportAction;
use Filament\Actions\Exports\Enums\ExportFormat;
use Filament\Forms\Components\Actions;
use Filament\Forms\Components\Actions\Action as FormAction;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\Page;
use Illuminate\Database\Eloquent\Builder;

class RidersReport extends Page implements HasForms
{
    use InteractsWithForms;

    protected static string $resource = ReportResource::class;

    protected static string $view = 'filament.resources.report-resource.pages.riders-report';

    protected static ?string $title = 'Riders Report';

    protected static ?string $navigationLabel = 'Riders Report';

    public ?array $data = [];

    public ?array $reportData = null;

    public bool $reportGenerated = false;

    public function mount(): void
    {
        $this->data = [
            'period' => 'total',
            'date_from' => null,
            'date_to' => null,
        ];

        $this->form->fill($this->data);
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Report Filters')
                    ->schema([
                        Select::make('period')
                            ->label('Time Period')
                            ->options([
                                'total' => 'Total (All Time)',
                                'weekly' => 'This Week',
                                'monthly' => 'This Month',
                                'custom' => 'Custom Range',
                            ])
                            ->native(false)

                            ->default('total')
                            ->required()
                            ->live()
                            ->afterStateUpdated(function ($state, callable $set) {
                                if ($state === 'weekly') {
                                    $set('date_from', now()->startOfWeek());
                                    $set('date_to', now()->endOfWeek());
                                } elseif ($state === 'monthly') {
                                    $set('date_from', now()->startOfMonth());
                                    $set('date_to', now()->endOfMonth());
                                } elseif ($state === 'total') {
                                    $set('date_from', null);
                                    $set('date_to', null);
                                }
                            }),

                        DatePicker::make('date_from')
                            ->label('From Date')
                            ->native(false)
                            ->visible(fn ($get) => in_array($get('period'), ['custom', 'weekly', 'monthly']))
                            ->disabled(fn ($get) => in_array($get('period'), ['weekly', 'monthly']))
                            ->required(fn ($get) => $get('period') === 'custom')
                            ->rules([
                                fn ($get) => function (string $_, $value, \Closure $fail) use ($get) {
                                    if ($get('period') === 'custom' && $get('date_to') && $value && Carbon::parse($value)->isAfter(Carbon::parse($get('date_to')))) {
                                        $fail('The from date must be before or equal to the to date.');
                                    }
                                },
                            ]),

                        DatePicker::make('date_to')
                            ->label('To Date')
                            ->native(false)
                            ->visible(fn ($get) => in_array($get('period'), ['custom', 'weekly', 'monthly']))
                            ->disabled(fn ($get) => in_array($get('period'), ['weekly', 'monthly']))
                            ->required(fn ($get) => $get('period') === 'custom')
                            ->rules([
                                fn ($get) => function (string $_, $value, \Closure $fail) use ($get) {
                                    if ($get('period') === 'custom' && $get('date_from') && $value && Carbon::parse($value)->isBefore(Carbon::parse($get('date_from')))) {
                                        $fail('The to date must be after or equal to the from date.');
                                    }
                                },
                            ]),

                        Actions::make([
                            FormAction::make('generate_report')
                                ->label('Generate Report')
                                ->icon('heroicon-o-chart-bar')
                                ->color('primary')
                                ->action('generateReport')
                                ->size('lg'),
                        ])
                            ->columnSpanFull()
                            ->alignment('center'),
                    ])
                    ->columns(3),
            ])
            ->statePath('data');
    }

    public function getHeaderActions(): array
    {
        $actions = [];

        // Only show export action if report has been generated
        if ($this->reportGenerated && $this->reportData !== null) {
            $actions[] = ExportAction::make()
                ->formats([
                    ExportFormat::Csv,
                ])
                ->exporter(RidersReportExporter::class)
                ->label('Export CSV')
                ->icon('heroicon-o-arrow-down-tray')
                ->color('success')
                ->modifyQueryUsing(function (Builder $query) {
                    // Capture the current form state at export time
                    try {
                        $currentFormState = $this->form->getState();
                        // Store the current state for use in getFilteredQuery
                        $this->data = array_merge($this->data ?? [], $currentFormState);
                    } catch (\Exception) {
                        // If form state is not accessible, use existing data
                    }

                    return $this->getFilteredQuery($query);
                });
        }

        return $actions;
    }

    public function generateReport(bool $showNotification = true): void
    {
        try {
            // Validate the form first
            $this->form->validate();

            // Get the form data
            $data = $this->form->getState();

            // Update component data to ensure consistency
            $this->data = array_merge($this->data ?? [], $data);

            // Generate the report data
            $this->reportData = $this->calculateRidersData($data);
            $this->reportGenerated = true;

            // Force a complete component refresh to update header actions
            $this->js('$wire.$refresh()');

            // Only show notification if explicitly requested (when user clicks the button)
            if ($showNotification) {
                Notification::make()
                    ->title('Report Generated Successfully')
                    ->success()
                    ->send();
            }

        } catch (\Illuminate\Validation\ValidationException $e) {
            // Re-throw validation exceptions to show form errors
            throw $e;
        } catch (\Exception $e) {
            // Only show error notification if explicitly requested
            if ($showNotification) {
                Notification::make()
                    ->title('Error Generating Report')
                    ->body('An error occurred while generating the report. Please try again.')
                    ->danger()
                    ->send();
            }
        }
    }

    protected function getFilteredQuery(Builder $query): Builder
    {
        // Use the current component data which should contain the latest form state
        // The export action now captures the form state before calling this method
        $data = $this->data ?? [
            'period' => 'total',
            'date_from' => null,
            'date_to' => null,
        ];

        $this->applyDateFilters($query, $data);

        return $query;
    }

    protected function applyDateFilters(Builder $query, array $data): Builder
    {
        if ($data['period'] === 'total') {
            // No date filtering for total period
            return $query;
        }

        // For weekly, monthly, and custom periods, apply date filters
        if (isset($data['date_from']) && isset($data['date_to']) && $data['date_from'] && $data['date_to']) {
            $query->whereBetween('created_at', [
                Carbon::parse($data['date_from'])->startOfDay(),
                Carbon::parse($data['date_to'])->endOfDay(),
            ]);
        }

        return $query;
    }

    public function getRidersData(): array
    {
        // Return cached report data if available
        if ($this->reportData !== null) {
            return $this->reportData;
        }

        // Fallback to default data if no report has been generated
        return [
            'total_riders' => 0,
            'status_distribution' => [],
            'growth_percentage' => 0,
            'period_info' => 'No report generated yet. Please click "Generate Report" to view data.',
        ];
    }

    protected function calculateRidersData(array $data): array
    {
        $query = Rider::query()->with('user');

        // Apply date filters
        $this->applyDateFilters($query, $data);

        $totalRiders = $query->count();

        // Get status distribution
        $statusDistribution = Rider::query();
        $this->applyDateFilters($statusDistribution, $data);
        $statusDistribution = $statusDistribution
            ->selectRaw('global_status, COUNT(*) as count')
            ->groupBy('global_status')
            ->pluck('count', 'global_status')
            ->toArray();

        // Calculate growth percentage
        $growthPercentage = $this->calculateGrowthPercentage($data);

        // Add period info
        $periodInfo = $this->getPeriodInfo($data);

        return [
            'total_riders' => $totalRiders,
            'status_distribution' => $statusDistribution,
            'growth_percentage' => $growthPercentage,
            'period_info' => $periodInfo,
        ];
    }

    protected function calculateGrowthPercentage(array $data): float
    {
        if ($data['period'] === 'total' || ! isset($data['date_from']) || ! isset($data['date_to']) || ! $data['date_from'] || ! $data['date_to']) {
            return 0;
        }

        $currentPeriodStart = Carbon::parse($data['date_from'])->startOfDay();
        $currentPeriodEnd = Carbon::parse($data['date_to'])->endOfDay();

        $periodLength = $currentPeriodStart->diffInDays($currentPeriodEnd) + 1;

        $previousPeriodStart = $currentPeriodStart->copy()->subDays($periodLength);
        $previousPeriodEnd = $currentPeriodStart->copy()->subDay();

        $currentCount = Rider::whereBetween('created_at', [$currentPeriodStart, $currentPeriodEnd])->count();
        $previousCount = Rider::whereBetween('created_at', [$previousPeriodStart, $previousPeriodEnd])->count();

        if ($previousCount === 0) {
            return $currentCount > 0 ? 100 : 0;
        }

        return round((($currentCount - $previousCount) / $previousCount) * 100, 2);
    }

    protected function getPeriodInfo(array $data): string
    {
        if ($data['period'] === 'total') {
            return 'All time data';
        }

        // Handle predefined periods
        if ($data['period'] === 'weekly') {
            $from = now()->startOfWeek()->format('M j, Y');
            $to = now()->endOfWeek()->format('M j, Y');

            return "This Week ({$from} to {$to})";
        }

        if ($data['period'] === 'monthly') {
            $from = now()->startOfMonth()->format('M j, Y');
            $to = now()->endOfMonth()->format('M j, Y');

            return "This Month ({$from} to {$to})";
        }

        // Handle custom date range
        if ($data['period'] === 'custom') {
            if (! isset($data['date_from']) || ! isset($data['date_to']) || ! $data['date_from'] || ! $data['date_to']) {
                return 'No date range selected';
            }

            $from = Carbon::parse($data['date_from'])->format('M j, Y');
            $to = Carbon::parse($data['date_to'])->format('M j, Y');

            return "Custom Range ({$from} to {$to})";
        }

        // Fallback for any other case
        if (isset($data['date_from']) && isset($data['date_to']) && $data['date_from'] && $data['date_to']) {
            $from = Carbon::parse($data['date_from'])->format('M j, Y');
            $to = Carbon::parse($data['date_to'])->format('M j, Y');

            return "From {$from} to {$to}";
        }

        return 'No date range selected';
    }

    public function getAverageTripsPerRider(): float
    {
        $totalRiders = Rider::count();
        $totalTrips = \App\Models\Trip::count();

        return $totalRiders > 0 ? round($totalTrips / $totalRiders, 1) : 0;
    }

    public function getRidersWithTrips(): int
    {
        return Rider::has('trips')->count();
    }
}
