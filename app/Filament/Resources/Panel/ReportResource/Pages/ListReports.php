<?php

namespace App\Filament\Resources\Panel\ReportResource\Pages;

use App\Filament\Resources\Panel\ReportResource;
use Filament\Resources\Pages\Page;

class ListReports extends Page
{
    protected static string $resource = ReportResource::class;

    protected static string $view = 'filament.resources.report-resource.pages.list-reports';

    protected static ?string $title = 'Reports Dashboard';

    protected static ?string $navigationLabel = 'Reports';

    public function getHeaderActions(): array
    {
        return [
            // Action::make('drivers_report')
            //     ->label('Drivers Report')
            //     ->icon('heroicon-o-users')
            //     ->color('primary')
            //     ->url(fn (): string => static::getResource()::getUrl('drivers')),

            // Action::make('riders_report')
            //     ->label('Riders Report')
            //     ->icon('heroicon-o-user-group')
            //     ->color('success')
            //     ->url(fn (): string => static::getResource()::getUrl('riders')),

            // Action::make('trips_report')
            //     ->label('Trips Report')
            //     ->icon('heroicon-o-map')
            //     ->color('warning')
            //     ->url(fn (): string => static::getResource()::getUrl('trips')),
        ];
    }

    protected function getFooterWidgets(): array
    {
        return [
            // Revenue Analytics - Commented out as requested
            // \App\Filament\Resources\Panel\ReportResource\Widgets\RevenueAnalyticsApexWidget::class,

            // Trip Activity Timeline - Commented out as requested
            // \App\Filament\Resources\Panel\ReportResource\Widgets\TripTimelineWidget::class,

            // User Growth Trends - Commented out as requested
            // \App\Filament\Resources\Panel\ReportResource\Widgets\UserGrowthApexWidget::class,

            // Driver Performance Metrics - Commented out as requested
            // \App\Filament\Resources\Panel\ReportResource\Widgets\DriverPerformanceRadarWidget::class,

            // Area Performance Heatmap - Commented out as requested
            // \App\Filament\Resources\Panel\ReportResource\Widgets\AreaRevenueHeatmapWidget::class,

            // Top Performers & Activity - Commented out as requested
            // \App\Filament\Resources\Panel\ReportResource\Widgets\TopPerformersWidget::class,
        ];
    }
}
