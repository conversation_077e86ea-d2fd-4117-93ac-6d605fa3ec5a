<?php

namespace App\Livewire\PricingRules;

use App\Models\Area;
use App\Rules\MinFareRule;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\ToggleButtons;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Notifications\Notification;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Concerns\InteractsWithTable;
use Filament\Tables\Contracts\HasTable;
use Filament\Tables\Table;
use Livewire\Component;

class AreaSpecificPricing extends Component implements HasForms, HasTable
{
    use InteractsWithForms;
    use InteractsWithTable;

    public function table(Table $table): Table
    {
        return $table
            ->query(Area::query())
            ->columns([
                TextColumn::make('name_ar')
                    ->label('Area Name (Arabic)'),

                TextColumn::make('base_fare_adjustment_type')
                    ->label('Base Fare Type')
                    ->badge(),

                TextColumn::make('base_fare_value')
                    ->label('Base Fare Adjustment'),

                TextColumn::make('distance_fare_adjustment_type')
                    ->label('Distance Fare Type')
                    ->badge(),

                TextColumn::make('distance_fare_value')
                    ->label('Distance Fare Adjustment'),
            ])
            ->filters([
                // ...
            ])
            ->paginationPageOptions([5, 10, 25, 50])
            ->actions([
                EditAction::make()
                    ->modalHeading('Update Area Pricing')
                    ->successNotification(
                        Notification::make()
                            ->success()
                            ->title('Update Successful')
                            ->body('Area pricing rules updated successfully.'),
                    )
                    ->form([
                        Section::make(fn ($record) => $record->name_ar)
                            ->schema([
                                ToggleButtons::make('base_fare_adjustment_type')
                                    ->label('Base Fare Type')
                                    ->grouped()
                                    ->options([
                                        'fixed' => 'Fixed',
                                        'percentage' => 'Percentage',
                                    ])
                                    ->reactive()
                                    ->helperText('Choose how the base fare will be adjusted.'),

                                TextInput::make('base_fare')
                                    ->label('Base Fare Adjustment')
                                    ->default('0')
                                    ->suffix('LYD')
                                    ->placeholder('e.g., 15.50')
                                    ->helperText('Enter the base fare for this area.')
                                    ->visible(fn (callable $get) => $get('base_fare_adjustment_type') === 'fixed')
                                    ->extraInputAttributes([
                                        'inputmode' => 'decimal',
                                        'pattern' => '[0-9.-]*',
                                        'maxlength' => 6,
                                        'oninput' => "this.value = this.value.replace(/[^0-9.-]/g, '').replace(/(\..*)\./g, '$1').replace(/^(-?)(.*)$/, (match, p1, p2) => p1 + p2.replace(/-/g, ''));", // Only allow one negative sign at the start
                                    ])
                                    ->rules(['required', 'numeric', 'lte:100', new MinFareRule('base')])
                                    ->validationMessages([
                                        'lte' => 'The base fare must not exceed 100 LYD.',
                                    ]),

                                TextInput::make('base_fare_adjustment')
                                    ->label('Base Fare Adjustment')
                                    ->default('0')
                                    ->suffix('%')
                                    ->placeholder('e.g., 10.25')
                                    ->helperText('Enter the percentage adjustment.')
                                    ->visible(fn (callable $get) => $get('base_fare_adjustment_type') === 'percentage')
                                    ->extraInputAttributes([
                                        'inputmode' => 'decimal',
                                        'pattern' => '[0-9.-]*',
                                        'maxlength' => 6,
                                        'oninput' => "this.value = this.value.replace(/[^0-9.-]/g, '').replace(/(\..*)\./g, '$1').replace(/^(-?)(.*)$/, (match, p1, p2) => p1 + p2.replace(/-/g, ''));", // Only allow one negative sign at the start
                                    ])
                                    ->rules(['required', 'numeric', 'gte:-50', 'lte:100'])
                                    ->validationMessages([
                                        'gte' => 'The base fare adjustment must be at least -50%.',
                                        'lte' => 'The base fare adjustment must not exceed 100%.',
                                    ]),

                                ToggleButtons::make('distance_fare_adjustment_type')
                                    ->label('Distance Fare Type')
                                    ->grouped()
                                    ->options([
                                        'fixed' => 'Fixed',
                                        'percentage' => 'Percentage',
                                    ])
                                    ->reactive()
                                    ->helperText('Choose how the distance fare will be adjusted.'),

                                TextInput::make('distance_fare')
                                    ->label('Distance Fare Adjustment')
                                    ->default('0')
                                    ->suffix('LYD')
                                    ->placeholder('e.g., 2.50')
                                    ->helperText('Enter the per-unit distance fare.')
                                    ->visible(fn (callable $get) => $get('distance_fare_adjustment_type') === 'fixed')
                                    ->extraInputAttributes([
                                        'inputmode' => 'decimal',
                                        'pattern' => '[0-9.-]*',
                                        'maxlength' => 6,
                                        'oninput' => "this.value = this.value.replace(/[^0-9.-]/g, '').replace(/(\..*)\./g, '$1').replace(/^(-?)(.*)$/, (match, p1, p2) => p1 + p2.replace(/-/g, ''));", // Only allow one negative sign at the start
                                    ])
                                    ->rules(['required', 'numeric', 'lte:100', new MinFareRule('distance')])
                                    ->validationMessages([
                                        'lte' => 'The distance fare must not exceed 100 LYD.',
                                    ]),

                                TextInput::make('distance_fare_adjustment')
                                    ->label('Distance Fare Adjustment')
                                    ->default('0')
                                    ->suffix('%')
                                    ->placeholder('e.g., 10.25')
                                    ->helperText('Enter the percentage adjustment.')
                                    ->visible(fn (callable $get) => $get('distance_fare_adjustment_type') === 'percentage')
                                    ->extraInputAttributes([
                                        'inputmode' => 'decimal',
                                        'pattern' => '[0-9.-]*',
                                        'maxlength' => 6,
                                        'oninput' => "this.value = this.value.replace(/[^0-9.-]/g, '').replace(/(\..*)\./g, '$1').replace(/^(-?)(.*)$/, (match, p1, p2) => p1 + p2.replace(/-/g, ''));", // Only allow one negative sign at the start
                                    ])
                                    ->rules(['required', 'numeric', 'gte:-50', 'lte:100'])
                                    ->validationMessages([
                                        'gte' => 'The distance fare adjustment must be at least -50%.',
                                        'lte' => 'The distance fare adjustment must not exceed 100%.',
                                    ]),
                            ])->columns(2),
                    ]),
            ])
            ->bulkActions([
                // ...
            ]);
    }

    public function render()
    {
        return view('livewire.pricing-rules.area-specific-pricing');
    }
}
