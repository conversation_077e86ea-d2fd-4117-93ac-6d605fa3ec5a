<?php

namespace App\Livewire\PricingRules;

use App\Models\PricingRuleAdditionalDayCharge;
use Filament\Forms\Components\Fieldset;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\ToggleButtons;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Notifications\Notification;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Concerns\InteractsWithTable;
use Filament\Tables\Contracts\HasTable;
use Filament\Tables\Table;
use Illuminate\Support\HtmlString;
use Livewire\Component;

class TimeBasedPricing extends Component implements HasForms, HasTable
{
    use InteractsWithForms, InteractsWithTable;

    public PricingRuleAdditionalDayCharge $record;

    public function table(Table $table): Table
    {
        return $table
            ->query(PricingRuleAdditionalDayCharge::query())
            ->columns([
                TextColumn::make('day')
                    ->label('Day of the Week')
                    ->formatStateUsing(fn ($state) => match ($state) {
                        'Sunday' => 'الأحد',
                        'Monday' => 'الإثنين',
                        'Tuesday' => 'الثلاثاء',
                        'Wednesday' => 'الأربعاء',
                        'Thursday' => 'الخميس',
                        'Friday' => 'الجمعة',
                        'Saturday' => 'السبت',
                        default => $state,
                    }),

                TextColumn::make('day_charge_type')
                    ->label('Day Base Fare Type')
                    ->badge()
                    ->formatStateUsing(fn ($state) => ucfirst($state)),

                TextColumn::make('day_charge_value')
                    ->label('Day Base Fare Adjustment')
                    ->formatStateUsing(function ($state, $record) {
                        return match ($record->day_charge_type) {
                            'fixed' => $record->day_fixed_charge ? number_format($record->day_fixed_charge, 2).' LYD' : '-',
                            'percentage' => $record->day_percentage_charge ? "{$record->day_percentage_charge}%" : '-',
                            default => '-',
                        };
                    }),

                TextColumn::make('day_distance_charge_type')
                    ->label('Day Distance Fare Type')
                    ->badge()
                    ->formatStateUsing(fn ($state) => ucfirst($state))
                    ->toggleable(isToggledHiddenByDefault: true),

                TextColumn::make('day_distance_charge_value')
                    ->label('Day Distance Fare Adjustment')
                    ->formatStateUsing(function ($state, $record) {
                        return match ($record->day_distance_charge_type) {
                            'fixed' => $record->day_distance_fixed_charge ? number_format($record->day_distance_fixed_charge, 2).' LYD' : '-',
                            'percentage' => $record->day_distance_percentage_charge ? "{$record->day_distance_percentage_charge}%" : '-',
                            default => '-',
                        };
                    })
                    ->toggleable(isToggledHiddenByDefault: true),

                TextColumn::make('night_charge_type')
                    ->label('Night Base Fare Type')
                    ->badge()
                    ->formatStateUsing(fn ($state) => ucfirst($state)),

                TextColumn::make('night_charge_value')
                    ->label('Night Base Fare Adjustment')
                    ->formatStateUsing(function ($state, $record) {
                        return match ($record->night_charge_type) {
                            'fixed' => $record->night_fixed_charge ? number_format($record->night_fixed_charge, 2).' LYD' : '-',
                            'percentage' => $record->night_percentage_charge ? "{$record->night_percentage_charge}%" : '-',
                            default => '-',
                        };
                    }),

                TextColumn::make('night_distance_charge_type')
                    ->label('Night Distance Fare Type')
                    ->badge()
                    ->formatStateUsing(fn ($state) => ucfirst($state))
                    ->toggleable(isToggledHiddenByDefault: true),

                TextColumn::make('night_distance_charge_value')
                    ->label('Night Distance Fare Adjustment')
                    ->formatStateUsing(function ($state, $record) {
                        return match ($record->night_distance_charge_type) {
                            'fixed' => $record->night_distance_fixed_charge ? number_format($record->night_distance_fixed_charge, 2).' LYD' : '-',
                            'percentage' => $record->night_distance_percentage_charge ? "{$record->night_distance_percentage_charge}%" : '-',
                            default => '-',
                        };
                    })
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->paginationPageOptions([5, 10, 25, 50])
            ->actions([
                EditAction::make()
                    ->modalHeading(fn ($record) => 'Modify '.ucfirst($record->day)."'s".' Day and Night Pricing')
                    ->successNotification(
                        Notification::make()
                            ->success()
                            ->title('Update Successful')
                            ->body('Day-Time pricing rules updated successfully.'),
                    )
                    ->form([
                        Hidden::make('day')
                            ->default(fn () => $this->record->day),

                        Tabs::make('Tabs')
                            ->tabs([
                                Tabs\Tab::make('Day')
                                    ->icon('mdi-sun-clock-outline')
                                    ->schema([
                                        ToggleButtons::make('day_charge_type')
                                            ->label('Base Fare Type')
                                            ->inline()
                                            ->options([
                                                'fixed' => 'Fixed',
                                                'percentage' => 'Percentage',
                                            ])
                                            ->grouped()
                                            ->required()
                                            ->reactive(),

                                        TextInput::make('day_fixed_charge')
                                            ->label('Base Fare Adjustment')
                                            ->default('0')
                                            ->rules(['required', 'numeric', 'lte:100'])
                                            ->maxValue(100)
                                            ->minValue(function ($get) {
                                                $pricingRule = \App\Models\PricingRules::first();
                                                $basePrice = $pricingRule?->global_base_price ?? 0;

                                                return -($basePrice / 2);
                                            })
                                            ->extraInputAttributes([
                                                'inputmode' => 'decimal',
                                                'pattern' => '[0-9.-]*',
                                                'maxlength' => 6,
                                                'oninput' => "this.value = this.value.replace(/[^0-9.-]/g, '').replace(/(\..*)\./g, '$1').replace(/^(-?)(.*)$/, (match, p1, p2) => p1 + p2.replace(/-/g, ''));",
                                            ])
                                            ->suffix('LYD')
                                            ->placeholder('Enter fixed charge for the day')
                                            ->helperText('Specify the fixed charge applicable during the day.')
                                            ->visible(fn ($get) => $get('day_charge_type') === 'fixed'),

                                        TextInput::make('day_percentage_charge')
                                            ->label('Base Fare Adjustment')
                                            ->default('0')
                                            ->suffix('%')
                                            ->rules(['required', 'numeric', 'gte:-50', 'lte:100'])
                                            ->extraInputAttributes([
                                                'inputmode' => 'decimal',
                                                'pattern' => '[0-9.-]*',
                                                'maxlength' => 6,
                                                'oninput' => "this.value = this.value.replace(/[^0-9.-]/g, '').replace(/(\..*)\./g, '$1').replace(/^(-?)(.*)$/, (match, p1, p2) => p1 + p2.replace(/-/g, ''));",
                                            ])
                                            ->placeholder('Enter percentage charge for the day')
                                            ->helperText('Specify the percentage charge for the day.')
                                            ->visible(fn ($get) => $get('day_charge_type') === 'percentage'),

                                        ToggleButtons::make('day_distance_charge_type')
                                            ->label('Distance Fare Type')
                                            ->inline()
                                            ->options([
                                                'fixed' => 'Fixed',
                                                'percentage' => 'Percentage',
                                            ])
                                            ->grouped()
                                            ->required()
                                            ->reactive(),

                                        TextInput::make('day_distance_fixed_charge')
                                            ->label('Distance Fare Adjustment')
                                            ->default('0')
                                            ->rules(['required', 'numeric', 'lte:100'])
                                            ->minValue(function ($get) {
                                                $pricingRule = \App\Models\PricingRules::first();
                                                $basePrice = $pricingRule?->global_price_per_km ?? 0;

                                                return -($basePrice / 2);
                                            })
                                            ->extraInputAttributes([
                                                'inputmode' => 'decimal',
                                                'pattern' => '[0-9.-]*',
                                                'maxlength' => 6,
                                                'oninput' => "this.value = this.value.replace(/[^0-9.-]/g, '').replace(/(\..*)\./g, '$1').replace(/^(-?)(.*)$/, (match, p1, p2) => p1 + p2.replace(/-/g, ''));",
                                            ])
                                            ->suffix('LYD')
                                            ->placeholder('Enter fixed charge per distance unit')
                                            ->helperText('Specify the fixed charge applicable per unit distance.')
                                            ->visible(fn ($get) => $get('day_distance_charge_type') === 'fixed'),

                                        TextInput::make('day_distance_percentage_charge')
                                            ->label('Distance Fare Adjustment')
                                            ->default('0')
                                            ->suffix('%')
                                            ->rules(['required', 'numeric', 'gte:-50', 'lte:100'])
                                            ->extraInputAttributes([
                                                'inputmode' => 'decimal',
                                                'pattern' => '[0-9.-]*',
                                                'maxlength' => 6,
                                                'oninput' => "this.value = this.value.replace(/[^0-9.-]/g, '').replace(/(\..*)\./g, '$1').replace(/^(-?)(.*)$/, (match, p1, p2) => p1 + p2.replace(/-/g, ''));",
                                            ])
                                            ->placeholder('Enter percentage charge per distance unit')
                                            ->helperText('Specify the percentage charge for the distance.')
                                            ->visible(fn ($get) => $get('day_distance_charge_type') === 'percentage'),
                                    ])->columns(2),

                                Tabs\Tab::make('Night')
                                    ->icon('mdi-weather-night')
                                    ->schema([
                                        ToggleButtons::make('night_charge_type')
                                            ->label('Base Fare Type')
                                            ->inline()
                                            ->options([
                                                'fixed' => 'Fixed',
                                                'percentage' => 'Percentage',
                                            ])
                                            ->grouped()
                                            ->required()
                                            ->reactive(),

                                        TextInput::make('night_fixed_charge')
                                            ->label('Base Fare Adjustment')
                                            ->rules(['required', 'numeric', 'lte:100'])
                                            ->default('0')
                                            ->suffix('LYD')
                                            ->maxValue(100)
                                            ->minValue(function ($get) {
                                                $pricingRule = \App\Models\PricingRules::first();
                                                $basePrice = $pricingRule?->global_base_price ?? 0;

                                                return -($basePrice / 2);
                                            })
                                            ->extraInputAttributes([
                                                'inputmode' => 'decimal',
                                                'pattern' => '[0-9.-]*',
                                                'maxlength' => 6,
                                                'oninput' => "this.value = this.value.replace(/[^0-9.-]/g, '').replace(/(\..*)\./g, '$1').replace(/^(-?)(.*)$/, (match, p1, p2) => p1 + p2.replace(/-/g, ''));",
                                            ])
                                            ->placeholder('Enter fixed charge for the night')
                                            ->helperText('Specify the fixed charge applicable during the night.')
                                            ->visible(fn ($get) => $get('night_charge_type') === 'fixed'),

                                        TextInput::make('night_percentage_charge')
                                            ->label('Base Fare Adjustment')
                                            ->default('0')
                                            ->suffix('%')
                                            ->rules(['required', 'numeric', 'gte:-50', 'lte:100'])
                                            ->extraInputAttributes([
                                                'inputmode' => 'decimal',
                                                'pattern' => '[0-9.-]*',
                                                'maxlength' => 6,
                                                'oninput' => "this.value = this.value.replace(/[^0-9.-]/g, '').replace(/(\..*)\./g, '$1').replace(/^(-?)(.*)$/, (match, p1, p2) => p1 + p2.replace(/-/g, ''));",
                                            ])
                                            ->placeholder('Enter percentage charge for the night')
                                            ->helperText('Specify the percentage charge for the night.')
                                            ->visible(fn ($get) => $get('night_charge_type') === 'percentage'),

                                        ToggleButtons::make('night_distance_charge_type')
                                            ->label('Distance Fare Type')
                                            ->inline()
                                            ->options([
                                                'fixed' => 'Fixed',
                                                'percentage' => 'Percentage',
                                            ])
                                            ->grouped()
                                            ->reactive(),

                                        TextInput::make('night_distance_fixed_charge')
                                            ->label('Distance Fare Adjustment')
                                            ->rules(['required', 'numeric'])
                                            ->default('0')
                                            ->maxValue(100)
                                            ->minValue(function ($get) {
                                                $pricingRule = \App\Models\PricingRules::first();
                                                $basePrice = $pricingRule?->global_price_per_km ?? 0;

                                                return -($basePrice / 2);
                                            })
                                            ->extraInputAttributes([
                                                'inputmode' => 'decimal',
                                                'pattern' => '[0-9.-]*',
                                                'maxlength' => 6,
                                                'oninput' => "this.value = this.value.replace(/[^0-9.-]/g, '').replace(/(\..*)\./g, '$1').replace(/^(-?)(.*)$/, (match, p1, p2) => p1 + p2.replace(/-/g, ''));",
                                            ])
                                            ->suffix('LYD')
                                            ->placeholder('Enter fixed charge per distance unit')
                                            ->helperText('Specify the fixed charge applicable per unit distance.')
                                            ->visible(fn ($get) => $get('night_distance_charge_type') === 'fixed'),

                                        TextInput::make('night_distance_percentage_charge')
                                            ->label('Distance Fare Adjustment')
                                            ->default('0')
                                            ->suffix('%')
                                            ->rules(['required', 'numeric', 'gte:-50', 'lte:100'])
                                            ->extraInputAttributes([
                                                'inputmode' => 'decimal',
                                                'pattern' => '[0-9.-]*',
                                                'maxlength' => 6,
                                                'oninput' => "this.value = this.value.replace(/[^0-9.-]/g, '').replace(/(\..*)\./g, '$1').replace(/^(-?)(.*)$/, (match, p1, p2) => p1 + p2.replace(/-/g, ''));",
                                            ])
                                            ->placeholder('Enter percentage charge per distance unit')
                                            ->helperText('Specify the percentage charge for the distance.')
                                            ->visible(fn ($get) => $get('night_distance_charge_type') === 'percentage'),
                                    ])->columns(2),

                                Tabs\Tab::make('Peak Hours')
                                    ->icon('mdi-timer-alert-outline')
                                    ->schema([
                                        Repeater::make('peak_hours')
                                            ->label('Peak Hours')
                                            ->relationship('peakHours')
                                            ->schema([
                                                Fieldset::make('Peak Start Time')
                                                    ->columnSpan(1)
                                                    ->columns(13)
                                                    ->schema([
                                                        Select::make('peak_start_hour')
                                                            ->hiddenLabel()
                                                            ->required()
                                                            ->placeholder('Select Hour')
                                                            ->columnSpan(6)
                                                            ->native(false)
                                                            ->dehydrated(false)
                                                            ->options([
                                                                '00' => '00', '01' => '01', '02' => '02',
                                                                '03' => '03', '04' => '04', '05' => '05',
                                                                '06' => '06', '07' => '07', '08' => '08',
                                                                '09' => '09', '10' => '10', '11' => '11',
                                                                '12' => '12', '13' => '13', '14' => '14',
                                                                '15' => '15', '16' => '16', '17' => '17',
                                                                '18' => '18', '19' => '19', '20' => '20',
                                                                '21' => '21', '22' => '22', '23' => '23',
                                                            ])
                                                            ->afterStateHydrated(function ($state, callable $set, callable $get) {
                                                                $hidden = $get('peak_start_at');
                                                                if ($hidden && str_contains($hidden, ':')) {
                                                                    $parts = explode(':', $hidden);
                                                                    $set('peak_start_hour', $parts[0]);
                                                                }
                                                            })
                                                            ->afterStateUpdated(function ($state, callable $set, callable $get) {
                                                                $minute = $get('peak_start_minute') ?? '00';
                                                                $set('peak_start_at', $state.':'.$minute);
                                                            }),
                                                        Placeholder::make('start_separator')
                                                            ->content(
                                                                function () {
                                                                    return new HtmlString('<span style="position: relative; top: 8px;">:</span>');
                                                                }
                                                            )
                                                            ->hiddenLabel()
                                                            ->columnSpan(1),

                                                        Select::make('peak_start_minute')
                                                            ->hiddenLabel()
                                                            ->placeholder('Select Minute')
                                                            ->columnSpan(6)
                                                            ->required()
                                                            ->native(false)
                                                            ->dehydrated(false)
                                                            ->options([
                                                                '00' => '00',
                                                                '15' => '15',
                                                                '30' => '30',
                                                                '45' => '45',
                                                            ])
                                                            ->afterStateHydrated(function ($state, callable $set, callable $get) {
                                                                $hidden = $get('peak_start_at');
                                                                if ($hidden && str_contains($hidden, ':')) {
                                                                    $parts = explode(':', $hidden);
                                                                    $set('peak_start_minute', $parts[1] ?? '00');
                                                                }
                                                            })
                                                            ->afterStateUpdated(function ($state, callable $set, callable $get) {
                                                                $hour = $get('peak_start_hour') ?? '00';
                                                                $set('peak_start_at', $hour.':'.$state);
                                                            }),

                                                        Hidden::make('peak_start_at')
                                                            ->live()
                                                            ->default('00:00'),
                                                    ]),

                                                Fieldset::make('Peak End Time')
                                                    ->columnSpan(1)
                                                    ->columns(13)
                                                    ->schema([
                                                        Select::make('peak_end_hour')
                                                            ->hiddenLabel()
                                                            ->required()
                                                            ->placeholder('Select Hour')
                                                            ->native(false)
                                                            ->dehydrated(false)
                                                            ->columnSpan(6)
                                                            ->options([
                                                                '00' => '00', '01' => '01', '02' => '02',
                                                                '03' => '03', '04' => '04', '05' => '05',
                                                                '06' => '06', '07' => '07', '08' => '08',
                                                                '09' => '09', '10' => '10', '11' => '11',
                                                                '12' => '12', '13' => '13', '14' => '14',
                                                                '15' => '15', '16' => '16', '17' => '17',
                                                                '18' => '18', '19' => '19', '20' => '20',
                                                                '21' => '21', '22' => '22', '23' => '23',
                                                            ])
                                                            ->afterStateHydrated(function ($state, callable $set, callable $get) {
                                                                $hidden = $get('peak_end_at');
                                                                if ($hidden && str_contains($hidden, ':')) {
                                                                    $parts = explode(':', $hidden);
                                                                    $set('peak_end_hour', $parts[0]);
                                                                }
                                                            })
                                                            ->afterStateUpdated(function ($state, callable $set, callable $get) {
                                                                $minute = $get('peak_end_minute') ?? '00';
                                                                $set('peak_end_at', $state.':'.$minute);
                                                            }),
                                                        Placeholder::make('start_separator')
                                                            ->content(
                                                                function () {
                                                                    return new HtmlString('<span style="position: relative; top: 8px;">:</span>');
                                                                }
                                                            )
                                                            ->hiddenLabel()
                                                            ->columnSpan(1),

                                                        Select::make('peak_end_minute')
                                                            ->hiddenLabel()
                                                            ->required()
                                                            ->placeholder('Select Minute')
                                                            ->columnSpan(6)
                                                            ->native(false)
                                                            ->dehydrated(false)
                                                            ->options([
                                                                '00' => '00',
                                                                '15' => '15',
                                                                '30' => '30',
                                                                '45' => '45',
                                                            ])
                                                            ->afterStateHydrated(function ($state, callable $set, callable $get) {
                                                                $hidden = $get('peak_end_at');
                                                                if ($hidden && str_contains($hidden, ':')) {
                                                                    $parts = explode(':', $hidden);
                                                                    $set('peak_end_minute', $parts[1] ?? '00');
                                                                }
                                                            })
                                                            ->afterStateUpdated(function ($state, callable $set, callable $get) {
                                                                $hour = $get('peak_end_hour') ?? '00';
                                                                $set('peak_end_at', $hour.':'.$state);
                                                            }),

                                                        Hidden::make('peak_end_at')
                                                            ->live()
                                                            ->default('00:00'),
                                                    ]),

                                                ToggleButtons::make('base_fare_adjustment_type')
                                                    ->label('Base Fare Type')
                                                    ->inline()
                                                    ->options([
                                                        'fixed' => 'Fixed',
                                                        'percentage' => 'Percentage',
                                                    ])
                                                    ->default('fixed')
                                                    ->grouped()
                                                    ->required()
                                                    ->reactive(),

                                                TextInput::make('base_fare_fixed')
                                                    ->label('Base Fare Adjustment')
                                                    ->default('0')
                                                    ->rules(['required', 'numeric', 'lte:100'])
                                                    ->minValue(function ($get) {
                                                        $pricingRule = \App\Models\PricingRules::first();
                                                        $basePrice = $pricingRule?->global_base_price ?? 0;

                                                        return -($basePrice / 2);
                                                    })
                                                    ->extraInputAttributes([
                                                        'inputmode' => 'decimal',
                                                        'pattern' => '[0-9.-]*',
                                                        'maxlength' => 6,
                                                        'oninput' => "this.value = this.value.replace(/[^0-9.-]/g, '').replace(/(\..*)\./g, '$1').replace(/^(-?)(.*)$/, (match, p1, p2) => p1 + p2.replace(/-/g, ''));",
                                                    ])
                                                    ->suffix('LYD')
                                                    ->placeholder('Enter fixed adjustment for the base fare')
                                                    ->helperText('Specify the fixed adjustment for the base fare during peak hours.')
                                                    ->visible(fn ($get) => $get('base_fare_adjustment_type') === 'fixed'),

                                                TextInput::make('base_fare_percentage')
                                                    ->label('Base Fare Adjustment')
                                                    ->default('0')
                                                    ->suffix('%')
                                                    ->rules(['required', 'numeric', 'gte:-50', 'lte:100'])
                                                    ->extraInputAttributes([
                                                        'inputmode' => 'decimal',
                                                        'pattern' => '[0-9.-]*',
                                                        'maxlength' => 6,
                                                        'oninput' => "this.value = this.value.replace(/[^0-9.-]/g, '').replace(/(\..*)\./g, '$1').replace(/^(-?)(.*)$/, (match, p1, p2) => p1 + p2.replace(/-/g, ''));",
                                                    ])
                                                    ->placeholder('Enter percentage adjustment for the base fare')
                                                    ->helperText('Specify the percentage adjustment for the base fare during peak hours.')
                                                    ->visible(fn ($get) => $get('base_fare_adjustment_type') === 'percentage'),

                                                ToggleButtons::make('distance_fare_adjustment_type')
                                                    ->label('Distance Fare Type')
                                                    ->inline()
                                                    ->default('fixed')
                                                    ->options([
                                                        'fixed' => 'Fixed',
                                                        'percentage' => 'Percentage',
                                                    ])
                                                    ->grouped()
                                                    ->reactive(),

                                                TextInput::make('distance_fare_fixed')
                                                    ->label('Distance Fare Adjustment')
                                                    ->rules(['required', 'numeric'])
                                                    ->default('0')
                                                    ->maxValue(100)
                                                    ->minValue(function ($get) {
                                                        $pricingRule = \App\Models\PricingRules::first();
                                                        $basePrice = $pricingRule?->global_price_per_km ?? 0;

                                                        return -($basePrice / 2);
                                                    })
                                                    ->extraInputAttributes([
                                                        'inputmode' => 'decimal',
                                                        'pattern' => '[0-9.-]*',
                                                        'maxlength' => 6,
                                                        'oninput' => "this.value = this.value.replace(/[^0-9.-]/g, '').replace(/(\..*)\./g, '$1').replace(/^(-?)(.*)$/, (match, p1, p2) => p1 + p2.replace(/-/g, ''));",
                                                    ])
                                                    ->suffix('LYD')
                                                    ->placeholder('Enter fixed adjustment for the distance fare')
                                                    ->helperText('Specify the fixed adjustment for the distance fare during peak hours.')
                                                    ->visible(fn ($get) => $get('distance_fare_adjustment_type') === 'fixed'),

                                                TextInput::make('distance_fare_percentage')
                                                    ->label('Distance Fare Adjustment')
                                                    ->default('0')
                                                    ->suffix('%')
                                                    ->rules(['required', 'numeric', 'gte:-50', 'lte:100'])
                                                    ->extraInputAttributes([
                                                        'inputmode' => 'decimal',
                                                        'pattern' => '[0-9.-]*',
                                                        'maxlength' => 6,
                                                        'oninput' => "this.value = this.value.replace(/[^0-9.-]/g, '').replace(/(\..*)\./g, '$1').replace(/^(-?)(.*)$/, (match, p1, p2) => p1 + p2.replace(/-/g, ''));",
                                                    ])
                                                    ->placeholder('Enter percentage adjustment for the distance fare')
                                                    ->helperText('Specify the percentage adjustment for the distance fare during peak hours.')
                                                    ->visible(fn ($get) => $get('distance_fare_adjustment_type') === 'percentage'),
                                            ])->collapsed()->reorderable()->columns(2),
                                    ])->columnSpanFull(),
                            ]),
                    ]),
            ])
            ->paginated(false)
            ->bulkActions([]);
    }

    public function save(): void
    {
        $data = $this->form->getState();
        $this->record->update($data);

        Notification::make()
            ->title('Update Successful')
            ->body('Time-based pricing has been updated successfully.')
            ->success()
            ->send();
    }

    public function render()
    {
        return view('livewire.pricing-rules.time-based-pricing');
    }
}
