<?php

namespace App\Models;

use App\Enums\Trips\CancellationStage;
use App\Enums\Trips\TripStatus;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Trip extends Model
{
    use HasFactory;

    protected $guarded = [];

    protected $casts = [
        'status' => TripStatus::class,
        'cancellation_stage' => CancellationStage::class,
        'is_favorite' => 'boolean',
        'estimated_departure_time' => 'datetime',
        'actual_departure_time' => 'datetime',
        'estimated_arrival_time' => 'datetime',
        'actual_arrival_time' => 'datetime',
        'driver_started_at' => 'datetime',
        'contacted_drivers' => 'array',
    ];

    protected $fillable = [
        'driver_started_at',
        'rider_id',
        'driver_id',
        'vehicle_id',
        'departure_area_id',
        'arrival_area_id',
        'distance',
        'estimated_departure_time',
        'actual_departure_time',
        'estimated_arrival_time',
        'actual_arrival_time',
        'pricing_breakdown',
        'is_favorite',
        'cancelled_by',
        'cancellation_stage',
        'rider_notes',
        'status',
        'vehicle_type_id',
        'is_female',
        'trip_location_id',
        'contacted_drivers',
        'share_token',
    ];

    public function rider()
    {
        return $this->belongsTo(Rider::class);
    }

    public function driver()
    {
        return $this->belongsTo(Driver::class);
    }

    public function vehicle()
    {
        return $this->belongsTo(Vehicle::class);
    }

    public function departureArea()
    {
        return $this->belongsTo(Area::class, 'departure_area_id');
    }

    public function arrivalArea()
    {
        return $this->belongsTo(Area::class, 'arrival_area_id');
    }

    public function tripLocation()
    {
        return $this->belongsTo(TripLocation::class, 'trip_location_id');
    }

    public function getPricingBreakdownArrayAttribute()
    {
        return json_decode($this->pricing_breakdown, true);
    }

    public function vehicleType()
    {
        return $this->belongsTo(TripVehicleType::class, 'vehicle_type_id');
    }

    public function refusedDrivers()
    {
        return $this->belongsToMany(Driver::class, 'trip_refused_drivers')
            ->withTimestamps()
            ->withPivot('reason');
    }

    public function tripRatings()
    {
        return $this->hasMany(TripRating::class);
    }

    /**
     * Get the cancellation record for the trip.
     */
    public function cancellation()
    {
        return $this->hasOne(TripCancellation::class);
    }

    public function storePricingBreakdown(array $pricing): void
    {
        $formattedPricing = [
            'base_fare' => round($pricing['base_fare'] ?? 0, 2),
            'per_km' => round($pricing['per_km'] ?? 0, 2),
            'distance' => round($pricing['distance'] ?? 0, 2),
            'subtotal' => round($pricing['subtotal'] ?? 0, 2),
            'total' => round($pricing['total'] ?? 0, 2),
            'currency' => $pricing['currency'] ?? 'LYD',
            'adjustments' => $pricing['adjustments'] ?? [],
            'price_changes' => [
                'original_price' => round($pricing['original_price'] ?? $pricing['total'] ?? 0, 2),
                'adjusted_price' => round($pricing['adjusted_price'] ?? $pricing['total'] ?? 0, 2),
            ],
            'timestamp' => now()->toIso8601String(),
        ];

        $this->update([
            'pricing_breakdown' => json_encode($formattedPricing),
        ]);
    }

    public function editRideRequests()
    {
        return $this->hasMany(EditRideRequest::class);
    }

    public function tripDuration()
    {
        // Check if both required timestamps exist
        if (! $this->driver_started_at || ! $this->actual_arrival_time) {
            return null;
        }

        // Parse timestamps to Carbon instances
        $startTime = \Carbon\Carbon::parse($this->driver_started_at);
        $endTime = \Carbon\Carbon::parse($this->actual_arrival_time);

        // Calculate the difference
        return $startTime->diff($endTime);
    }
}
