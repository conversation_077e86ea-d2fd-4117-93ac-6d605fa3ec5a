<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Artisan;

class TestClustering extends Command
{
    protected $signature = 'test:clustering {--clear : Clear existing test data first}';

    /**
     * The console command description.
     *
     * @var string
     */
    //protected $description = 'Create test data to demonstrate vehicle clustering functionality';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🚀 Setting up clustering test data...');

        if ($this->option('clear')) {
            $this->info('🧹 Clearing existing test data...');

            // Clear test drivers and vehicles
            \DB::table('driver_vehicle')->where('vehicle_id', '>', 10)->delete();
            \DB::table('vehicles')->where('license_plate_number', 'LIKE', 'CLU-%')->delete();
            \DB::table('vehicles')->where('license_plate_number', 'LIKE', 'TEST-%')->delete();
            \DB::table('drivers')->where('id', '>', 10)->delete();
            \DB::table('users')->where('email', 'LIKE', '%cluster-driver%')->delete();
            \DB::table('users')->where('email', 'LIKE', '%test-driver%')->delete();

            $this->info('✅ Test data cleared');
        }

        $this->info('🎯 Creating clustered test data...');
        Artisan::call('db:seed', ['--class' => 'ClusteredDriverLocationSeeder']);
        $this->info('✅ Clustered test data created');

        $this->info('');
        $this->info('🗺️  Clustering Test Setup Complete!');
        $this->info('');
        $this->info('📊 What was created:');
        $this->info('- 50 drivers in 5 clusters around Tripoli');
        $this->info('- Tripoli Center: 15 drivers');
        $this->info('- Tripoli Airport: 8 drivers');
        $this->info('- Tripoli Port: 12 drivers');
        $this->info('- Tajoura: 6 drivers');
        $this->info('- Janzour: 9 drivers');
        $this->info('');
        $this->info('🧪 How to test clustering:');
        $this->info('1. Navigate to the admin panel');
        $this->info('2. View the Vehicle Real-Time Movement widget');
        $this->info('3. Zoom out to see cluster markers (blue/green/red circles with numbers)');
        $this->info('4. Zoom in to see clusters break apart into individual vehicle markers');
        $this->info('5. Click on cluster markers to zoom in automatically');
        $this->info('6. Click on individual vehicle markers to see details');
        $this->info('');
        $this->info('🎨 Cluster colors:');
        $this->info('- Blue: 2-4 vehicles');
        $this->info('- Green: 5-9 vehicles');
        $this->info('- Red: 10+ vehicles');
    }
}
