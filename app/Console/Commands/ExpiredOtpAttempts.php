<?php

namespace App\Console\Commands;

use App\Models\Auth\OtpBlock;
use App\Models\Auth\OtpRequest;
use Carbon\Carbon;
use Illuminate\Console\Command;

class ExpiredOtpAttempts extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:expired-otp-attempts';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = "A Commande Used To Delete Expired Phone And Email Verification Otp's Attempts";

    /**
     * Execute the console command.
     */
    public function handle()
    {
        // Get the current time in UTC and subtract one day
        $thisDay = Carbon::now()->subDay()->endOfDay();

        OtpRequest::where('created_at', '<=', $thisDay)->delete();
        OtpBlock::where('created_at', '<=', $thisDay)->delete();

        $this->info('Deleted old OTP attempts for email and phone number.');

        \Log::info('Deleted old OTP attempts for email and phone number at : '.$thisDay.'.');
    }
}
