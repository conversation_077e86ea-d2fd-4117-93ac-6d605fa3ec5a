<?php

namespace App\Console\Commands;

use Database\Seeders\ReportsTestSeeder;
use Illuminate\Console\Command;

class SeedReportsTestData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'reports:seed-test-data {--fresh : Clear existing drivers before seeding}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Seed test data for reports functionality with drivers distributed throughout the year';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🚀 Seeding test data for reports functionality...');

        if ($this->option('fresh')) {
            $this->warn('⚠️  Clearing existing drivers...');

            if ($this->confirm('This will delete all existing drivers and their related data. Continue?')) {
                \App\Models\Driver::query()->forceDelete();
                \App\Models\User::where('type', 'driver')->forceDelete();
                $this->info('✅ Cleared existing driver data');
            } else {
                $this->info('❌ Operation cancelled');

                return;
            }
        }

        // Run the reports test seeder
        $this->call('db:seed', [
            '--class' => ReportsTestSeeder::class,
        ]);

        $this->newLine();
        $this->info('🎉 Reports test data seeding completed!');
        $this->info('📊 You can now test the reports at: /admin/reports/drivers, /admin/reports/riders, /admin/reports/trips');
        $this->newLine();
    }
}
