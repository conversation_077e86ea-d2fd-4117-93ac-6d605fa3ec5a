<?php

namespace App\Console\Commands;

use Database\Seeders\TestTripDurationSeeder;
use Illuminate\Console\Command;

class TestTripDuration extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:trip-duration';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create test trips with realistic timing data to test duration display';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Creating test trips with realistic timing data...');

        $seeder = new TestTripDurationSeeder;
        $seeder->run();

        $this->info('Test trips created successfully!');
        $this->info('You can now check the backoffice to see the new duration fields.');

        return 0;
    }
}
