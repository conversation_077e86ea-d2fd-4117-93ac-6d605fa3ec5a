<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Artisan;

class SeedTestData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:seed-data {--fresh : Fresh migration and seed}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Seed test data for vehicle tracking and trip management testing';

    /**
     * Execute the console command
     */
    public function handle()
    {
        $this->info('🚀 Starting test data seeding process...');
        
        if ($this->option('fresh')) {
            $this->info('🔄 Running fresh migrations...');
            Artisan::call('migrate:fresh');
            $this->info('✅ Fresh migrations completed');
        }
        
        $this->info('🌱 Running basic seeders...');
        
        // Run essential seeders first
        $basicSeeders = [
            'AdminPanelUserSeeder',
            'VehicleBrandsTableSeeder',
            'VehicleModelsTableSeeder', 
            'VehicleTypesTableSeeder',
            'VehicleEquipmentTableSeeder',
            'AreaSeeder',
            'AreasTableSeeder',
            'AddressSeeder',
            'AddressLabelSeeder',
            'PaymentSeeder',
            'PricingRulesSeeder',
        ];
        
        foreach ($basicSeeders as $seeder) {
            $this->info("Running {$seeder}...");
            try {
                Artisan::call('db:seed', ['--class' => $seeder]);
                $this->info("✅ {$seeder} completed");
            } catch (\Exception $e) {
                $this->warn("⚠️  {$seeder} failed: " . $e->getMessage());
            }
        }
        
        $this->info('🚗 Running driver location seeder with test scenarios...');
        Artisan::call('db:seed', ['--class' => 'DriverLocationSeeder']);
        $this->info('✅ Driver location seeder completed');
        
        $this->info('🔄 Syncing vehicle statuses...');
        Artisan::call('vehicles:sync-status');
        $this->info('✅ Vehicle statuses synced');
        
        $this->info('🎯 Test data seeding completed successfully!');
        $this->info('');
        $this->info('📊 Summary:');
        $this->info('- 100 drivers created across 5 Libyan cities');
        $this->info('- 100 vehicles with different statuses');
        $this->info('- Active trips with realistic scenarios');
        $this->info('- Available drivers ready for dispatch');
        $this->info('- Completed trip history');
        $this->info('');
        $this->info('🗺️  You can now test the vehicle tracking widget!');
        $this->info('Navigate to the admin panel and check the Vehicle Real-Time Movement widget.');
    }
}
