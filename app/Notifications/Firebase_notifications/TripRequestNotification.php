<?php

namespace App\Notifications\Firebase_notifications;

use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Notification;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use NotificationChannels\Fcm\FcmChannel;
use NotificationChannels\Fcm\FcmMessage;
use NotificationChannels\Fcm\Resources\Notification as FcmNotification;
use Throwable;

class TripRequestNotification extends Notification implements ShouldQueue
{
    use Queueable, SerializesModels;

    protected $data;
    protected $userId;
    protected $tripId;

    public $tries = 3;
    public $maxExceptions = 3;
    public $backoff = 10;

    public function __construct($user_id, $data, $trip_id = null)
    {
        $this->userId = $user_id;
        $this->data = $data;
        $this->tripId = $trip_id;
    }

    public function via($notifiable)
    {
        return [FcmChannel::class];
    }

    protected function getUser()
    {
        return User::findOrFail($this->userId);
    }

    public function toFcm($notifiable): FcmMessage
    {
        try {
            $notif = new FcmMessage(notification: new FcmNotification(
                title: $this->data['title'],
                body: $this->data['description'],
            ));

            return $notif
                ->data([
                    'type' => 'trip_request',
                    'trip_id' => $this->tripId,
                    'notification_id' => uniqid('trip_'),
                    'action' => 'new_trip_request',
                ])
                ->custom([
                    'android' => [
                        'notification' => [
                            'color' => '#0A0A0A',
                            'channel_id' => 'trip_requests',
                            'sound' => 'default',
                            'priority' => 'high',
                        ],
                        'fcm_options' => [
                            'analytics_label' => 'trip_request',
                        ],
                    ],
                    'apns' => [
                        'headers' => [
                            'apns-priority' => '10',
                            'apns-push-type' => 'alert',
                        ],
                        'payload' => [
                            'aps' => [
                                'alert' => [
                                    'title' => $this->data['title'],
                                    'body' => $this->data['description'],
                                ],
                                'sound' => 'default',
                                'badge' => 1,
                                'category' => 'TRIP_REQUEST',
                            ],
                        ],
                        'fcm_options' => [
                            'analytics_label' => 'trip_request',
                        ],
                    ],
                ]);
        } catch (Throwable $e) {
            Log::error('Trip Request FCM Notification Creation Failed', [
                'user_id' => $this->userId,
                'trip_id' => $this->tripId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            throw $e;
        }
    }

    public function failed(Throwable $exception)
    {
        try {
            $user = $this->getUser();
            $tokens = $user->tokens->pluck('token')->toArray();
        } catch (Throwable $e) {
            $tokens = [];
        }

        Log::error('Trip Request FCM Notification Failed to Send', [
            'user_id' => $this->userId,
            'trip_id' => $this->tripId,
            'title' => $this->data['title'],
            'error' => $exception->getMessage(),
            'tokens' => $tokens,
        ]);
    }
}
