<?php

namespace App\Notifications\Firebase_notifications;

use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Notification;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use NotificationChannels\Fcm\FcmChannel;
use NotificationChannels\Fcm\FcmMessage;
use NotificationChannels\Fcm\Resources\Notification as FcmNotification;
use Throwable;

class TripStatusNotification extends Notification implements ShouldQueue
{
    use Queueable, SerializesModels;

    protected $data;
    protected $userId;
    protected $tripId;
    protected $status;

    public $tries = 3;
    public $maxExceptions = 3;
    public $backoff = 10;

    public function __construct($user_id, $data, $trip_id = null, $status = null)
    {
        $this->userId = $user_id;
        $this->data = $data;
        $this->tripId = $trip_id;
        $this->status = $status;
    }

    public function via($notifiable)
    {
        return [FcmChannel::class];
    }

    protected function getUser()
    {
        return User::findOrFail($this->userId);
    }

    public function toFcm($notifiable): FcmMessage
    {
        try {
            $notif = new FcmMessage(notification: new FcmNotification(
                title: $this->data['title'],
                body: $this->data['description'],
            ));

            return $notif
                ->data([
                    'type' => 'trip_status',
                    'trip_id' => $this->tripId,
                    'status' => $this->status,
                    'notification_id' => uniqid('trip_status_'),
                    'action' => 'trip_status_update',
                ])
                ->custom([
                    'android' => [
                        'notification' => [
                            'color' => '#0A0A0A',
                            'channel_id' => 'trip_updates',
                            'sound' => 'default',
                        ],
                        'fcm_options' => [
                            'analytics_label' => 'trip_status',
                        ],
                    ],
                    'apns' => [
                        'headers' => [
                            'apns-priority' => '10',
                            'apns-push-type' => 'alert',
                        ],
                        'payload' => [
                            'aps' => [
                                'alert' => [
                                    'title' => $this->data['title'],
                                    'body' => $this->data['description'],
                                ],
                                'sound' => 'default',
                                'badge' => 1,
                                'category' => 'TRIP_STATUS',
                            ],
                        ],
                        'fcm_options' => [
                            'analytics_label' => 'trip_status',
                        ],
                    ],
                ]);
        } catch (Throwable $e) {
            Log::error('Trip Status FCM Notification Creation Failed', [
                'user_id' => $this->userId,
                'trip_id' => $this->tripId,
                'status' => $this->status,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            throw $e;
        }
    }

    public function failed(Throwable $exception)
    {
        try {
            $user = $this->getUser();
            $tokens = $user->tokens->pluck('token')->toArray();
        } catch (Throwable $e) {
            $tokens = [];
        }

        Log::error('Trip Status FCM Notification Failed to Send', [
            'user_id' => $this->userId,
            'trip_id' => $this->tripId,
            'status' => $this->status,
            'title' => $this->data['title'],
            'error' => $exception->getMessage(),
            'tokens' => $tokens,
        ]);
    }
}
