<?php

namespace App\Services;

use App\Enums\Trips\TripStatus;
use App\Events\DriverEvents\RideRequest;
use App\Models\Driver;
use App\Models\Trip;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class DriverRequestService
{
    /**
     * Send a ride request to a driver with collision prevention
     */
    public function sendRequest(int $driverId, array $tripDetails, int $tripId): bool
    {
        // First, check if the trip is still in dispatched status
        $trip = Trip::find($tripId);
        if (! $trip || $trip->status->value !== TripStatus::dispatched->value) {
            Log::info('Skipping request - trip is no longer dispatched', [
                'driver_id' => $driverId,
                'trip_id' => $tripId,
                'status' => $trip ? $trip->status->value : 'not_found',
            ]);

            return false;
        }

        // Create a unique lock key for this driver
        $lockKey = "driver_request_lock_{$driverId}";

        // Try to acquire a lock - if we can't, another request is being processed
        // We use a 10-second lock to prevent deadlocks
        $lockAcquired = Cache::add($lockKey, $tripId, 10);
        Log::info('Lock acquired', [
            'driver_id' => $driverId,
            'trip_id' => $tripId,
            'lock_acquired' => $lockAcquired,
        ]);

        if (! $lockAcquired) {
            // Check if the lock exists but is stale (from a canceled trip)
            $lockedTripId = Cache::get($lockKey);
            $trip = Trip::find($lockedTripId);

            // If the trip is canceled or doesn't exist, force release the lock
            if (! $trip || $trip->status->value === TripStatus::canceled->value) {
                Cache::forget($lockKey);
                // Try to acquire the lock again
                $lockAcquired = Cache::add($lockKey, $tripId, 10);
            }

            if (! $lockAcquired) {
                // Another request is still active for this driver
                Log::info('Driver is busy with another request, skipping', [
                    'driver_id' => $driverId,
                    'trip_id' => $tripId,
                ]);

                return false; // Return false because driver is busy
            }
        }

        try {
            // Send the WebSocket request
            broadcast(new RideRequest($driverId, $tripDetails));

            // Send push notification to driver using the service
            $driver = Driver::find($driverId);
            if ($driver && $driver->user) {
                app(\App\Services\TripNotificationService::class)->sendTripRequestNotification($driver->user, $tripId);
            }

            Log::info('Sent request to driver', [
                'driver_id' => $driverId,
                'trip_id' => $tripId,
            ]);

            // Release the lock after a short delay
            dispatch(function () use ($lockKey) {
                // Release the lock
                Cache::forget($lockKey);
            })->delay(now()->addSeconds(2));

            return true;
        } catch (\Exception $e) {
            // If broadcasting fails, release the lock
            Cache::forget($lockKey);
            Log::error('Failed to send request to driver', [
                'driver_id' => $driverId,
                'trip_id' => $tripId,
                'error' => $e->getMessage(),
            ]);

            throw $e;
        }
    }

    /**
     * Handle a driver's response to a request
     */
    public function handleResponse(int $driverId, int $tripId, string $response): void
    {
        $lockKey = "driver_request_lock_{$driverId}";

        // Release the lock if this is the active request
        Cache::forget($lockKey);

        Log::info('Driver responded to request', [
            'driver_id' => $driverId,
            'trip_id' => $tripId,
            'response' => $response,
        ]);
    }

    /**
     * Release a driver's lock when a trip is cancelled
     */
    public function releaseDriverLock(int $driverId): void
    {
        $lockKey = "driver_request_lock_{$driverId}";

        // Release the lock immediately
        Cache::forget($lockKey);

        Log::info('Released driver lock due to trip cancellation', [
            'driver_id' => $driverId,
        ]);
    }
}
