<?php

namespace App\Services;

use App\Models\Trip;
use App\Models\User;
use App\Notifications\Firebase_notifications\TripCancellationNotification;
use App\Notifications\Firebase_notifications\TripRequestNotification;
use App\Notifications\Firebase_notifications\TripStatusNotification;
use Illuminate\Support\Facades\Log;

class TripNotificationService
{
    /**
     * Send trip cancellation notification to the appropriate user
     */
    public function sendCancellationNotification(Trip $trip, string $cancelledBy): void
    {
        if ($cancelledBy === 'rider') {
            $this->notifyDriverAboutCancellation($trip, $cancelledBy);
        } elseif ($cancelledBy === 'driver') {
            $this->notifyRiderAboutCancellation($trip, $cancelledBy);
        }
    }

    /**
     * Send trip status notification to rider
     */
    public function sendTripStatusNotification(Trip $trip, string $status, array $notificationData): void
    {
        if (! $trip->rider || ! $trip->rider->user || ! $trip->rider->user->fcm_token) {
            return;
        }

        try {
            $trip->rider->user->notifyNow(new TripStatusNotification(
                $trip->rider->user->id,
                $notificationData,
                $trip->id,
                $status
            ));

            Log::info("Trip {$status} push notification sent to rider", [
                'trip_id' => $trip->id,
                'rider_id' => $trip->rider->id,
                'status' => $status,
            ]);
        } catch (\Exception $notificationException) {
            Log::error("Failed to send trip {$status} notification to rider", [
                'trip_id' => $trip->id,
                'rider_id' => $trip->rider->id,
                'status' => $status,
                'error' => $notificationException->getMessage(),
            ]);
        }
    }

    /**
     * Send trip start notification to rider
     */
    public function sendTripStartNotification(Trip $trip): void
    {
        $notificationData = [
            'title' => 'السائق في الطريق إليك',
            'description' => 'السائق في الطريق إليك',
        ];

        $this->sendTripStatusNotification($trip, 'on_trip', $notificationData);
    }

    /**
     * Send trip completion notification to rider
     */
    public function sendTripCompletionNotification(Trip $trip): void
    {
        $notificationData = [
            'title' => 'تم إنهاء الرحلة بنجاح',
            'description' => 'تم إنهاء الرحلة بنجاح. شكراً لاستخدام خدمتنا',
        ];

        $this->sendTripStatusNotification($trip, 'completed', $notificationData);
    }

    /**
     * Notify driver about trip cancellation by rider
     */
    private function notifyDriverAboutCancellation(Trip $trip, string $cancelledBy): void
    {
        if (! $trip->driver || ! $trip->driver->user || ! $trip->driver->user->fcm_token) {
            return;
        }

        $notificationData = [
            'title' => 'لقد تم إلغاء الرحلة',
            'description' => 'لقد تم إلغاء الرحلة من قبل الراكب',
        ];

        try {
            $trip->driver->user->notifyNow(new TripCancellationNotification(
                $trip->driver->user->id,
                $notificationData,
                $trip->id,
                $cancelledBy
            ));

            Log::info('Trip cancellation push notification sent to driver', [
                'trip_id' => $trip->id,
                'driver_id' => $trip->driver->id,
                'cancelled_by' => $cancelledBy,
            ]);
        } catch (\Exception $notificationException) {
            Log::error('Failed to send trip cancellation notification to driver', [
                'trip_id' => $trip->id,
                'driver_id' => $trip->driver->id,
                'error' => $notificationException->getMessage(),
            ]);
        }
    }

    /**
     * Notify rider about trip cancellation by driver
     */
    private function notifyRiderAboutCancellation(Trip $trip, string $cancelledBy): void
    {
        if (! $trip->rider || ! $trip->rider->user || ! $trip->rider->user->fcm_token) {
            return;
        }

        $notificationData = [
            'title' => 'لقد تم إلغاء الرحلة',
            'description' => 'لقد تم إلغاء الرحلة من قبل السائق',
        ];

        try {
            $trip->rider->user->notifyNow(new TripCancellationNotification(
                $trip->rider->user->id,
                $notificationData,
                $trip->id,
                $cancelledBy
            ));

            Log::info('Trip cancellation push notification sent to rider', [
                'trip_id' => $trip->id,
                'rider_id' => $trip->rider->id,
                'cancelled_by' => $cancelledBy,
            ]);
        } catch (\Exception $notificationException) {
            Log::error('Failed to send trip cancellation notification to rider', [
                'trip_id' => $trip->id,
                'rider_id' => $trip->rider->id,
                'error' => $notificationException->getMessage(),
            ]);
        }
    }

    /**
     * Send driver arrival notification to rider
     */
    public function sendDriverArrivalNotification(Trip $trip): void
    {
        $notificationData = [
            'title' => 'لقد وصل السائق، الرجاء التوجه إلى موقع الانطلاق',
            'description' => 'لقد وصل السائق، الرجاء التوجه إلى موقع الانطلاق',
        ];

        $this->sendTripStatusNotification($trip, 'driver_arrived', $notificationData);
    }

    /**
     * Send trip assignment notification to driver
     */
    public function sendTripAssignmentNotification(Trip $trip): void
    {
        if (! $trip->driver || ! $trip->driver->user || ! $trip->driver->user->fcm_token) {
            return;
        }

        $notificationData = [
            'title' => 'رحلة جديدة',
            'description' => 'تم تعيين رحلة جديدة لك',
        ];

        try {
            $trip->driver->user->notifyNow(new TripStatusNotification(
                $trip->driver->user->id,
                $notificationData,
                $trip->id,
                'assigned'
            ));

            Log::info('Trip assignment push notification sent to driver', [
                'trip_id' => $trip->id,
                'driver_id' => $trip->driver->id,
            ]);
        } catch (\Exception $notificationException) {
            Log::error('Failed to send trip assignment notification to driver', [
                'trip_id' => $trip->id,
                'driver_id' => $trip->driver->id,
                'error' => $notificationException->getMessage(),
            ]);
        }
    }

    /**
     * Send trip request notification to driver
     */
    public function sendTripRequestNotification(User $driverUser, int $tripId): void
    {
        if (! $driverUser->fcm_token) {
            return;
        }

        $notificationData = [
            'title' => 'لديك طلب رحلة جديد. اضغط هنا لمزيد من المعلومات.',
            'description' => 'لديك طلب رحلة جديد. اضغط هنا لمزيد من المعلومات.',
        ];

        try {
            $driverUser->notifyNow(new TripRequestNotification(
                $driverUser->id,
                $notificationData,
                $tripId
            ));

            Log::info('Trip request push notification sent to driver', [
                'driver_user_id' => $driverUser->id,
                'trip_id' => $tripId,
            ]);
        } catch (\Exception $notificationException) {
            Log::error('Failed to send trip request notification to driver', [
                'driver_user_id' => $driverUser->id,
                'trip_id' => $tripId,
                'error' => $notificationException->getMessage(),
            ]);
        }
    }

    /**
     * Send custom notification to a specific user
     */
    public function sendCustomNotification(User $user, string $notificationClass, array $data, ?int $tripId = null, ?string $status = null): void
    {
        if (! $user->fcm_token) {
            return;
        }

        try {
            $notification = match ($notificationClass) {
                TripCancellationNotification::class => new TripCancellationNotification(
                    $user->id,
                    $data['notification_data'],
                    $tripId,
                    $status
                ),
                TripStatusNotification::class => new TripStatusNotification(
                    $user->id,
                    $data['notification_data'],
                    $tripId,
                    $status
                ),
                default => throw new \InvalidArgumentException("Unsupported notification class: {$notificationClass}")
            };

            $user->notifyNow($notification);

            Log::info('Custom notification sent successfully', [
                'user_id' => $user->id,
                'notification_class' => $notificationClass,
                'trip_id' => $tripId,
                'status' => $status,
            ]);
        } catch (\Exception $notificationException) {
            Log::error('Failed to send custom notification', [
                'user_id' => $user->id,
                'notification_class' => $notificationClass,
                'trip_id' => $tripId,
                'status' => $status,
                'error' => $notificationException->getMessage(),
            ]);
        }
    }
}
