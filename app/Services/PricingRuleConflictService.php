<?php

namespace App\Services;

use App\Enums\Payments\PaymentTypeEnum;
use App\Models\Area;
use App\Models\PricingRuleGender;
use App\Models\PricingRulePeakHour;
use App\Models\PricingRuleSeatNumber;
use App\Models\VehicleType;

class PricingRuleConflictService
{
    /**
     * Check for conflicts when both base and distance prices are being changed
     *
     * @param  float  $newGlobalBasePrice  The new global base price
     * @param  float  $newGlobalPricePerKm  The new global price per km
     * @param  float  $currentGlobalBasePrice  The current global base price
     * @param  float  $currentGlobalPricePerKm  The current global price per km
     * @return array Array with combined conflicts for both base and distance
     */
    public function checkForCombinedConflicts(
        float $newGlobalBasePrice,
        float $newGlobalPricePerKm,
        float $currentGlobalBasePrice,
        float $currentGlobalPricePerKm
    ): array {
        $combinedConflicts = [
            'base_fare_conflicts' => [],
            'distance_fare_conflicts' => [],
        ];

        // Check base fare conflicts if it's being decreased
        if ($newGlobalBasePrice < $currentGlobalBasePrice) {
            $baseConflicts = $this->checkForConflicts($newGlobalBasePrice, $currentGlobalPricePerKm);
            if (! empty($baseConflicts)) {
                $combinedConflicts['base_fare_conflicts'] = $baseConflicts;
            }
        }

        // Check distance fare conflicts if it's being decreased
        if ($newGlobalPricePerKm < $currentGlobalPricePerKm) {
            $distanceConflicts = $this->checkForConflicts($currentGlobalBasePrice, $newGlobalPricePerKm);
            if (! empty($distanceConflicts)) {
                $combinedConflicts['distance_fare_conflicts'] = $distanceConflicts;
            }
        }

        return $combinedConflicts;
    }

    /**
     * Check for conflicts between global pricing rules and component-specific pricing adjustments
     *
     * @param  float  $newGlobalBasePrice  The new global base price
     * @param  float  $newGlobalPricePerKm  The new global price per km
     * @return array Array of conflicts found
     */
    public function checkForConflicts(float $newGlobalBasePrice, float $newGlobalPricePerKm): array
    {
        $conflicts = [];
        $conflictChecks = [
            'areas' => $this->checkAreaConflicts($newGlobalBasePrice, $newGlobalPricePerKm),
            'vehicle_types' => $this->checkVehicleTypeConflicts($newGlobalBasePrice, $newGlobalPricePerKm),
            'seat_numbers' => $this->checkSeatNumberConflicts($newGlobalBasePrice, $newGlobalPricePerKm),
            'gender_rules' => $this->checkGenderConflicts($newGlobalBasePrice, $newGlobalPricePerKm),
            'equipment' => $this->checkEquipmentConflicts($newGlobalBasePrice, $newGlobalPricePerKm),
            'day_time_configs' => $this->checkDayTimeConflicts($newGlobalBasePrice, $newGlobalPricePerKm),
        ];

        foreach ($conflictChecks as $key => $checkResults) {

            if (! empty($checkResults)) {
                $conflicts[$key] = $checkResults;
            }
        }

        return $conflicts;
    }

    /**
     * Check for conflicts when only base fare is being changed
     * This is used by the PricingRuleConflictRule when validating base fare changes
     *
     * @param  float  $newGlobalBasePrice  The new global base price
     * @param  float  $currentGlobalPricePerKm  The current global price per km (unchanged)
     * @return array Array of conflicts found
     */
    public function checkForBaseFareConflicts(float $newGlobalBasePrice, float $currentGlobalPricePerKm): array
    {
        $conflicts = [];
        $conflictChecks = [
            'areas' => $this->checkAreaConflicts($newGlobalBasePrice, $currentGlobalPricePerKm),
            'vehicle_types' => $this->checkVehicleTypeConflicts($newGlobalBasePrice, $currentGlobalPricePerKm),
            'seat_numbers' => $this->checkSeatNumberConflicts($newGlobalBasePrice, $currentGlobalPricePerKm),
            'gender_rules' => $this->checkGenderConflicts($newGlobalBasePrice, $currentGlobalPricePerKm),
            'equipment' => $this->checkEquipmentConflicts($newGlobalBasePrice, $currentGlobalPricePerKm),
            'day_time_configs' => $this->checkDayTimeConflicts($newGlobalBasePrice, $currentGlobalPricePerKm),
        ];

        foreach ($conflictChecks as $key => $checkResults) {
            if (! empty($checkResults)) {
                $conflicts[$key] = $checkResults;
            }
        }

        return $conflicts;
    }

    /**
     * Check for conflicts when only distance fare is being changed
     * This is used by the PricingRuleConflictRule when validating distance fare changes
     * Equipment conflicts are excluded since they're only related to base fare
     *
     * @param  float  $currentGlobalBasePrice  The current global base price (unchanged)
     * @param  float  $newGlobalPricePerKm  The new global price per km
     * @return array Array of conflicts found
     */
    public function checkForDistanceFareConflicts(float $currentGlobalBasePrice, float $newGlobalPricePerKm): array
    {
        $conflicts = [];
        $conflictChecks = [
            'areas' => $this->checkAreaConflicts($currentGlobalBasePrice, $newGlobalPricePerKm),
            'vehicle_types' => $this->checkVehicleTypeConflicts($currentGlobalBasePrice, $newGlobalPricePerKm),
            'seat_numbers' => $this->checkSeatNumberConflicts($currentGlobalBasePrice, $newGlobalPricePerKm),
            'gender_rules' => $this->checkGenderConflicts($currentGlobalBasePrice, $newGlobalPricePerKm),
            // Equipment conflicts are excluded for distance fare changes
            'day_time_configs' => $this->checkDayTimeConflicts($currentGlobalBasePrice, $newGlobalPricePerKm),
        ];

        foreach ($conflictChecks as $key => $checkResults) {
            if (! empty($checkResults)) {
                $conflicts[$key] = $checkResults;
            }
        }

        return $conflicts;
    }

    /**
     * Check for conflicts in area pricing
     *
     * @param  float  $newGlobalBasePrice  The new global base price
     * @param  float  $newGlobalPricePerKm  The new global price per km
     * @return array Array of conflicts found
     */
    private function checkAreaConflicts(float $newGlobalBasePrice, float $newGlobalPricePerKm): array
    {
        $minBaseValue = $this->calculateMinValue($newGlobalBasePrice);
        $minDistanceValue = $this->calculateMinValue($newGlobalPricePerKm);

        $areas = Area::where(function ($query) {
            $query->where('base_fare_adjustment_type', PaymentTypeEnum::fixed)
                ->orWhere('distance_fare_adjustment_type', PaymentTypeEnum::fixed);
        })->get();

        $conflicts = [];

        foreach ($areas as $area) {
            $areaConflict = $this->checkComponentConflicts(
                $area,
                $minBaseValue,
                $minDistanceValue,
                'base_fare',
                'distance_fare',
                'base_fare_adjustment_type',
                'distance_fare_adjustment_type'
            );

            if (! empty($areaConflict)) {
                $conflicts[] = [
                    'id' => $area->id,
                    'name' => $area->name_en,
                    'conflicts' => $areaConflict,
                ];
            }
        }

        return $conflicts;
    }

    /**
     * Check for conflicts in vehicle type pricing
     *
     * @param  float  $newGlobalBasePrice  The new global base price
     * @param  float  $newGlobalPricePerKm  The new global price per km
     * @return array Array of conflicts found
     */
    private function checkVehicleTypeConflicts(float $newGlobalBasePrice, float $newGlobalPricePerKm): array
    {
        $minBaseValue = $this->calculateMinValue($newGlobalBasePrice);
        $minDistanceValue = $this->calculateMinValue($newGlobalPricePerKm);

        $vehicleTypes = VehicleType::where(function ($query) {
            $query->where('base_fare_adjustment_type', PaymentTypeEnum::fixed)
                ->orWhere('distance_fare_adjustment_type', PaymentTypeEnum::fixed);
        })->get();

        $conflicts = [];

        foreach ($vehicleTypes as $vehicleType) {
            $vehicleTypeConflict = $this->checkComponentConflicts(
                $vehicleType,
                $minBaseValue,
                $minDistanceValue,
                'additional_base_fare',
                'additional_price_per_km',
                'base_fare_adjustment_type',
                'distance_fare_adjustment_type'
            );

            if (! empty($vehicleTypeConflict)) {
                $conflicts[] = [
                    'id' => $vehicleType->id,
                    'name' => $vehicleType->name_en,
                    'conflicts' => $vehicleTypeConflict,
                ];
            }
        }

        return $conflicts;
    }

    /**
     * Check for conflicts in seat number pricing
     *
     * @param  float  $newGlobalBasePrice  The new global base price
     * @param  float  $newGlobalPricePerKm  The new global price per km
     * @return array Array of conflicts found
     */
    private function checkSeatNumberConflicts(float $newGlobalBasePrice, float $newGlobalPricePerKm): array
    {
        $minBaseValue = $this->calculateMinValue($newGlobalBasePrice);
        $minDistanceValue = $this->calculateMinValue($newGlobalPricePerKm);

        $seatNumbers = PricingRuleSeatNumber::where(function ($query) {
            $query->where('base_fare_adjustment_type', PaymentTypeEnum::fixed)
                ->orWhere('distance_fare_adjustment_type', PaymentTypeEnum::fixed);
        })->get();

        $conflicts = [];

        foreach ($seatNumbers as $seatNumber) {
            $seatNumberConflict = $this->checkComponentConflicts(
                $seatNumber,
                $minBaseValue,
                $minDistanceValue,
                'base_fare',
                'distance_fare',
                'base_fare_adjustment_type',
                'distance_fare_adjustment_type'
            );

            if (! empty($seatNumberConflict)) {
                $conflicts[] = [
                    'id' => $seatNumber->id,
                    'seats_number' => $seatNumber->seats_number,
                    'conflicts' => $seatNumberConflict,
                ];
            }
        }

        return $conflicts;
    }

    /**
     * Check for conflicts in gender pricing
     *
     * @param  float  $newGlobalBasePrice  The new global base price
     * @param  float  $newGlobalPricePerKm  The new global price per km
     * @return array Array of conflicts found
     */
    private function checkGenderConflicts(float $newGlobalBasePrice, float $newGlobalPricePerKm): array
    {
        $minBaseValue = $this->calculateMinValue($newGlobalBasePrice);
        $minDistanceValue = $this->calculateMinValue($newGlobalPricePerKm);

        $genderRules = PricingRuleGender::where(function ($query) {
            $query->where('base_fare_adjustment_type', PaymentTypeEnum::fixed)
                ->orWhere('distance_fare_adjustment_type', PaymentTypeEnum::fixed);
        })->get();

        $conflicts = [];

        foreach ($genderRules as $genderRule) {
            $genderConflict = $this->checkComponentConflicts(
                $genderRule,
                $minBaseValue,
                $minDistanceValue,
                'base_fare_fixed',
                'distance_fare_fixed',
                'base_fare_adjustment_type',
                'distance_fare_adjustment_type'
            );

            if (! empty($genderConflict)) {
                $conflicts[] = [
                    'id' => $genderRule->id,
                    'gender' => $genderRule->gender->value,
                    'conflicts' => $genderConflict,
                ];
            }
        }

        return $conflicts;
    }

    /**
     * Check for conflicts in equipment pricing
     * Equipment conflicts are only checked against base fare changes since equipment
     * is a fixed additional cost that's conceptually related to base pricing
     * Following the same business logic as other components
     *
     * @param  float  $newGlobalBasePrice  The new global base price
     * @param  float  $newGlobalPricePerKm  The new global price per km
     * @return array Array of conflicts found
     */
    private function checkEquipmentConflicts(float $newGlobalBasePrice, float $newGlobalPricePerKm): array
    {
        // Follow the same business logic as other components
        // Calculate minimum allowed value (-50% of global base price)
        $minBaseValue = $this->calculateMinValue($newGlobalBasePrice);

        $equipments = \App\Models\VehicleEquipment::where('status', true)
            ->whereNotNull('additional_fare')
            ->where('additional_fare', '!=', 0)
            ->get();

        $conflicts = [];

        foreach ($equipments as $equipment) {
            $equipmentCost = (float) $equipment->additional_fare;

            // Check if equipment additional_fare is below the minimum allowed value
            // This follows the same logic as other components (areas, vehicle types, etc.)
            if ($equipmentCost < $minBaseValue) {
                $conflicts[] = [
                    'id' => $equipment->id,
                    'name' => $equipment->name_en,
                    'conflicts' => [
                        'base_fare' => [
                            'current_value' => $equipmentCost,
                            'min_allowed' => $minBaseValue,
                        ],
                    ],
                ];
            }
        }

        return $conflicts;
    }

    /**
     * Check for conflicts in day-time configuration pricing
     *
     * @param  float  $newGlobalBasePrice  The new global base price
     * @param  float  $newGlobalPricePerKm  The new global price per km
     * @return array Array of conflicts found
     */
    private function checkDayTimeConflicts(float $newGlobalBasePrice, float $newGlobalPricePerKm): array
    {
        $minBaseValue = $this->calculateMinValue($newGlobalBasePrice);
        $minDistanceValue = $this->calculateMinValue($newGlobalPricePerKm);

        $conflicts = [];

        // Check Peak Hours
        $peakHours = PricingRulePeakHour::with('dayCharge')->where(function ($query) {
            $query->where('base_fare_adjustment_type', PaymentTypeEnum::fixed)
                ->orWhere('distance_fare_adjustment_type', PaymentTypeEnum::fixed);
        })->get();

        foreach ($peakHours as $peakHour) {
            $peakHourConflict = $this->checkComponentConflicts(
                $peakHour,
                $minBaseValue,
                $minDistanceValue,
                'base_fare_fixed',
                'distance_fare_fixed',
                'base_fare_adjustment_type',
                'distance_fare_adjustment_type'
            );

            if (! empty($peakHourConflict)) {
                // Get day name from related day charge
                $dayName = $peakHour->dayCharge ? ucfirst($peakHour->dayCharge->day) : 'Unknown Day';

                // Format times without seconds
                $startTime = \Carbon\Carbon::parse($peakHour->peak_start_at)->format('H:i');
                $endTime = \Carbon\Carbon::parse($peakHour->peak_end_at)->format('H:i');
                $peakHourName = "{$dayName} Peak Hour ({$startTime} - {$endTime})";

                $conflicts[] = [
                    'id' => $peakHour->id,
                    'name' => $peakHourName,
                    'conflicts' => $peakHourConflict,
                ];
            }
        }

        // Check Day/Night Charges
        $dayCharges = \App\Models\PricingRuleAdditionalDayCharge::where(function ($query) {
            $query->where('day_charge_type', 'fixed')
                ->orWhere('night_charge_type', 'fixed')
                ->orWhere('day_distance_charge_type', 'fixed')
                ->orWhere('night_distance_charge_type', 'fixed');
        })->get();

        foreach ($dayCharges as $dayCharge) {
            // Get the day name (handle both English and Arabic)
            $dayName = ucfirst($dayCharge->day ?? 'Unknown Day');

            // Check day charges
            if ($dayCharge->day_charge_type === 'fixed') {
                if ((float) $dayCharge->day_fixed_charge < $minBaseValue) {
                    // Format times without seconds
                    $dayStartTime = \Carbon\Carbon::parse($dayCharge->day_start_at)->format('H:i');
                    $dayEndTime = \Carbon\Carbon::parse($dayCharge->day_end_at)->format('H:i');
                    $conflictName = "{$dayName} Day Charge ({$dayStartTime} - {$dayEndTime})";

                    $conflicts[] = [
                        'id' => $dayCharge->id,
                        'name' => $conflictName,
                        'conflicts' => [
                            'base_fare' => [
                                'current_value' => $dayCharge->day_fixed_charge,
                                'min_allowed' => $minBaseValue,
                            ],
                        ],
                    ];
                }
            }

            // Check night charges
            if ($dayCharge->night_charge_type === 'fixed') {
                if ((float) $dayCharge->night_fixed_charge < $minBaseValue) {
                    // Format times without seconds
                    $nightStartTime = \Carbon\Carbon::parse($dayCharge->night_start_at)->format('H:i');
                    $nightEndTime = \Carbon\Carbon::parse($dayCharge->night_end_at)->format('H:i');

                    $conflicts[] = [
                        'id' => $dayCharge->id,
                        'name' => "{$dayName} Night Charge ({$nightStartTime} - {$nightEndTime})",
                        'conflicts' => [
                            'base_fare' => [
                                'current_value' => $dayCharge->night_fixed_charge,
                                'min_allowed' => $minBaseValue,
                            ],
                        ],
                    ];
                }
            }

            // Check day distance charges
            if ($dayCharge->day_distance_charge_type === 'fixed') {
                if ((float) $dayCharge->day_distance_fixed_charge < $minDistanceValue) {
                    // Format times without seconds
                    $dayStartTime = \Carbon\Carbon::parse($dayCharge->day_start_at)->format('H:i');
                    $dayEndTime = \Carbon\Carbon::parse($dayCharge->day_end_at)->format('H:i');

                    $conflicts[] = [
                        'id' => $dayCharge->id,
                        'name' => "{$dayName} Day Distance Charge ({$dayStartTime} - {$dayEndTime})",
                        'conflicts' => [
                            'distance_fare' => [
                                'current_value' => $dayCharge->day_distance_fixed_charge,
                                'min_allowed' => $minDistanceValue,
                            ],
                        ],
                    ];
                }
            }

            // Check night distance charges
            if ($dayCharge->night_distance_charge_type === 'fixed') {
                if ((float) $dayCharge->night_distance_fixed_charge < $minDistanceValue) {
                    // Format times without seconds
                    $nightStartTime = \Carbon\Carbon::parse($dayCharge->night_start_at)->format('H:i');
                    $nightEndTime = \Carbon\Carbon::parse($dayCharge->night_end_at)->format('H:i');

                    $conflicts[] = [
                        'id' => $dayCharge->id,
                        'name' => "{$dayName} Night Distance Charge ({$nightStartTime} - {$nightEndTime})",
                        'conflicts' => [
                            'distance_fare' => [
                                'current_value' => $dayCharge->night_distance_fixed_charge,
                                'min_allowed' => $minDistanceValue,
                            ],
                        ],
                    ];
                }
            }
        }

        return $conflicts;
    }

    /**
     * Check component for base and distance fare conflicts
     *
     * @param  mixed  $component  The component to check
     * @param  float  $minBaseValue  Minimum allowed base value
     * @param  float  $minDistanceValue  Minimum allowed distance value
     * @param  string  $baseField  Field name for base fare
     * @param  string  $distanceField  Field name for distance fare
     * @param  string  $baseTypeField  Field name for base fare adjustment type
     * @param  string  $distanceTypeField  Field name for distance fare adjustment type
     * @return array Component conflicts
     */
    private function checkComponentConflicts(
        $component,
        float $minBaseValue,
        float $minDistanceValue,
        string $baseField,
        string $distanceField,
        string $baseTypeField,
        string $distanceTypeField
    ): array {
        $conflicts = [];

        // Get the base type value, handling both enum objects and strings
        $baseTypeValue = $component->{$baseTypeField};
        if (is_object($baseTypeValue) && property_exists($baseTypeValue, 'value')) {
            $baseTypeValue = $baseTypeValue->value;
        }

        // Get the distance type value, handling both enum objects and strings
        $distanceTypeValue = $component->{$distanceTypeField};
        if (is_object($distanceTypeValue) && property_exists($distanceTypeValue, 'value')) {
            $distanceTypeValue = $distanceTypeValue->value;
        }

        if (
            $baseTypeValue === 'fixed' &&
            (float) $component->{$baseField} < $minBaseValue
        ) {
            $conflicts['base_fare'] = [
                'current_value' => $component->{$baseField},
                'min_allowed' => $minBaseValue,
            ];
        }

        if (
            $distanceTypeValue === 'fixed' &&
            (float) $component->{$distanceField} < $minDistanceValue
        ) {
            $conflicts['distance_fare'] = [
                'current_value' => $component->{$distanceField},
                'min_allowed' => $minDistanceValue,
            ];
        }

        return $conflicts;
    }

    /**
     * Calculate the minimum value based on the global price
     *
     * @param  float  $globalPrice  The global price
     * @return float The minimum value (-50% of global price)
     */
    private function calculateMinValue(float $globalPrice): float
    {
        $minValue = round(-($globalPrice / 2), 2);

        return $minValue;
    }
}
