@php
  $isDisabled = $isDisabled();
  $state = $getState();
  $isRTL = config('filament-rating-star.isRTL');
  $direction = $isRTL ? 'rtl' : 'ltr';
  $size = match ($getSize()) {
    'xs' => 12,
    'sm' => 18,
    'md' => 22,
    'lg' => 28,
    'xl' => 36,
  };

  // Convert state to float for decimal handling
  $rating = (float) $state;
  $maxStars = count(config('filament-rating-star.stars'));
@endphp

<div class="flex justify-center p-10" dir="{{ $direction }}">
  @for ($i = 1; $i <= $maxStars; $i++)
    @php
    // Determine star type: full, half, or empty
    $starType = 'empty';
    if ($rating >= $i) {
    $starType = 'full';
    } elseif ($rating >= ($i - 0.5)) {
    $starType = 'half';
    }
  @endphp

    <div class="relative" style="width: {{ $size }}px; height: {{ $size }}px;">
    @if ($starType === 'full')
    {{-- Full star --}}
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 30 30" width="{{ $size }}" height="{{ $size }}">
      <path
      d="M15.765,2.434l2.875,8.512l8.983,0.104c0.773,0.009,1.093,0.994,0.473,1.455l-7.207,5.364l2.677,8.576 c0.23,0.738-0.607,1.346-1.238,0.899L15,22.147l-7.329,5.196c-0.63,0.447-1.468-0.162-1.238-0.899l2.677-8.576l-7.207-5.364 c-0.62-0.461-0.3-1.446,0.473-1.455l8.983-0.104l2.875-8.512C14.482,1.701,15.518,1.701,15.765,2.434z"
      fill="#ffc107" />
    </svg>
    @elseif ($starType === 'half')
    {{-- Half star --}}
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 30 30" width="{{ $size }}" height="{{ $size }}">
      <defs>
      <linearGradient id="half-fill-{{ $i }}" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="50%" style="stop-color:#ffc107;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#ddd;stop-opacity:1" />
      </linearGradient>
      </defs>
      <path
      d="M15.765,2.434l2.875,8.512l8.983,0.104c0.773,0.009,1.093,0.994,0.473,1.455l-7.207,5.364l2.677,8.576 c0.23,0.738-0.607,1.346-1.238,0.899L15,22.147l-7.329,5.196c-0.63,0.447-1.468-0.162-1.238-0.899l2.677-8.576l-7.207-5.364 c-0.62-0.461-0.3-1.446,0.473-1.455l8.983-0.104l2.875-8.512C14.482,1.701,15.518,1.701,15.765,2.434z"
      fill="url(#half-fill-{{ $i }})" />
    </svg>
    @else
    {{-- Empty star --}}
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 30 30" width="{{ $size }}" height="{{ $size }}">
      <path
      d="M15.765,2.434l2.875,8.512l8.983,0.104c0.773,0.009,1.093,0.994,0.473,1.455l-7.207,5.364l2.677,8.576 c0.23,0.738-0.607,1.346-1.238,0.899L15,22.147l-7.329,5.196c-0.63,0.447-1.468-0.162-1.238-0.899l2.677-8.576l-7.207-5.364 c-0.62-0.461-0.3-1.446,0.473-1.455l8.983-0.104l2.875-8.512C14.482,1.701,15.518,1.701,15.765,2.434z"
      fill="#ddd" />
    </svg>
    @endif
    </div>
  @endfor
</div>