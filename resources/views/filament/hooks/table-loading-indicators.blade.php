<script>
    // Global Filament Table Loading Indicators
    document.addEventListener('DOMContentLoaded', function () {
        initializeTableLoadingIndicators();
    });

    // Re-initialize on Livewire navigation
    document.addEventListener('livewire:navigated', function () {
        initializeTableLoadingIndicators();
    });

    function initializeTableLoadingIndicators() {
        // Add CSS styles for loading overlay
        if (!document.getElementById('filament-table-loading-styles')) {
            const style = document.createElement('style');
            style.id = 'filament-table-loading-styles';
            style.textContent = `
                .fi-ta-ctn {
                    position: relative;
                }
                
                .sr-only {
                    position: absolute;
                    width: 1px;
                    height: 1px;
                    padding: 0;
                    margin: -1px;
                    overflow: hidden;
                    clip: rect(0, 0, 0, 0);
                    white-space: nowrap;
                    border: 0;
                }
                
                .filament-table-loading-bar {
                    position: absolute;
                    bottom: 0;
                    left: 0;
                    right: 0;
                    height: 3px;
                    background: rgba(229, 231, 235, 0.3);
                    border-radius: 0 0 0.75rem 0.75rem;
                    overflow: hidden;
                    display: none;
                    z-index: 10;
                }

                .dark .filament-table-loading-bar {
                    background: rgba(75, 85, 99, 0.3);
                }

                .filament-table-loading-progress {
                    height: 100%;
                    background: linear-gradient(90deg, #f6b130, #f8a34d, #f78232);
                    border-radius: 0 0 0.75rem 0.75rem;
                    animation: progressSlide 1.5s ease-in-out infinite;
                    width: 100%;
                    transform: translateX(-100%);
                }

                .dark .filament-table-loading-progress {
                    background: linear-gradient(90deg, #f6b130, #f8a34d, #f78232);
                }
                
                @keyframes progressSlide {
                    0% {
                        transform: translateX(-100%);
                    }
                    50% {
                        transform: translateX(0%);
                    }
                    100% {
                        transform: translateX(100%);
                    }
                }
                
                /* Responsive design for smaller screens */
                @media (max-width: 640px) {
                    .filament-table-loading-bar {
                        height: 2px;
                    }
                }

                /* Accessibility improvements */
                @media (prefers-reduced-motion: reduce) {
                    .filament-table-loading-progress {
                        animation: none;
                        transform: translateX(0);
                        background: #f6b130;
                    }
                }

                /* High contrast mode support */
                @media (prefers-contrast: high) {
                    .filament-table-loading-bar {
                        background: rgba(0, 0, 0, 0.2);
                    }

                    .dark .filament-table-loading-bar {
                        background: rgba(255, 255, 255, 0.2);
                    }

                    .filament-table-loading-progress {
                        background: #f6b130;
                    }
                }

                /* Disabled state for pagination controls */
                .filament-table-disabled {
                    pointer-events: none;
                    opacity: 0.6;
                    cursor: not-allowed;
                }

                .filament-table-disabled button,
                .filament-table-disabled a {
                    pointer-events: none;
                    opacity: 0.5;
                    cursor: not-allowed;
                }

                /* Loading state for individual buttons */
                .filament-pagination-loading {
                    position: relative;
                    pointer-events: none;
                    opacity: 0.7;
                }

                .filament-pagination-loading::after {
                    content: '';
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    width: 16px;
                    height: 16px;
                    margin: -8px 0 0 -8px;
                    border: 2px solid transparent;
                    border-top: 2px solid currentColor;
                    border-radius: 50%;
                    animation: spin 1s linear infinite;
                    z-index: 1;
                }

                @keyframes spin {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                }
            `;
            document.head.appendChild(style);
        }

        // Track blocked tables to prevent multiple rapid clicks
        const blockedTables = new Set();

        // Function to block table interactions during loading
        const blockTableInteractions = (container) => {
            if (blockedTables.has(container)) return;

            blockedTables.add(container);

            // Disable pagination controls
            const paginationControls = container.querySelectorAll(
                '.fi-ta-pagination, .fi-ta-pagination-records-per-page-selector, nav[role="navigation"]'
            );
            paginationControls.forEach(control => {
                control.classList.add('filament-table-disabled');
            });

            // Disable individual pagination buttons and links
            const paginationButtons = container.querySelectorAll(
                '.fi-ta-pagination button, .fi-ta-pagination a, nav[role="navigation"] button, nav[role="navigation"] a, .fi-ta-pagination-records-per-page-selector select'
            );
            paginationButtons.forEach(button => {
                button.setAttribute('data-original-disabled', button.disabled || false);
                button.disabled = true;
                button.style.pointerEvents = 'none';
            });

            // Disable table actions and filters
            const tableActions = container.querySelectorAll(
                '.fi-ta-actions button, .fi-ta-header-actions button, .fi-ta-filters button, .fi-ta-search input'
            );
            tableActions.forEach(action => {
                action.setAttribute('data-original-disabled', action.disabled || false);
                action.disabled = true;
            });
        };

        // Function to unblock table interactions after loading
        const unblockTableInteractions = (container) => {
            if (!blockedTables.has(container)) return;

            blockedTables.delete(container);

            // Re-enable pagination controls
            const paginationControls = container.querySelectorAll('.filament-table-disabled');
            paginationControls.forEach(control => {
                control.classList.remove('filament-table-disabled');
            });

            // Re-enable pagination buttons and links
            const paginationButtons = container.querySelectorAll(
                '.fi-ta-pagination button, .fi-ta-pagination a, nav[role="navigation"] button, nav[role="navigation"] a, .fi-ta-pagination-records-per-page-selector select'
            );
            paginationButtons.forEach(button => {
                const wasOriginallyDisabled = button.getAttribute('data-original-disabled') === 'true';
                if (!wasOriginallyDisabled) {
                    button.disabled = false;
                }
                button.style.pointerEvents = '';
                button.removeAttribute('data-original-disabled');
            });

            // Re-enable table actions and filters
            const tableActions = container.querySelectorAll(
                '.fi-ta-actions button, .fi-ta-header-actions button, .fi-ta-filters button, .fi-ta-search input'
            );
            tableActions.forEach(action => {
                const wasOriginallyDisabled = action.getAttribute('data-original-disabled') === 'true';
                if (!wasOriginallyDisabled) {
                    action.disabled = false;
                }
                action.removeAttribute('data-original-disabled');
            });
        };

        // Function to add loading bar to table containers
        const addLoadingBarToTables = () => {
            // Find all Filament table containers
            const tableContainers = document.querySelectorAll('.fi-ta-ctn');

            tableContainers.forEach(container => {
                // Skip if loading bar already exists
                if (container.querySelector('.filament-table-loading-bar')) {
                    return;
                }

                // Create and add loading bar
                const loadingBar = document.createElement('div');
                loadingBar.className = 'filament-table-loading-bar';
                loadingBar.innerHTML = `
                    <div class="filament-table-loading-progress"></div>
                    <span class="sr-only">Loading table data</span>
                `;

                // Add wire:loading attributes to show/hide loading bar
                loadingBar.setAttribute('wire:loading.block', '');
                loadingBar.setAttribute('wire:target', 'gotoPage,previousPage,nextPage,sortTable,tableSearch,tableFilters,tableRecordsPerPage');

                // Add accessibility attributes
                loadingBar.setAttribute('role', 'progressbar');
                loadingBar.setAttribute('aria-label', 'Loading table data');
                loadingBar.setAttribute('aria-live', 'polite');

                container.appendChild(loadingBar);

                // Add event listeners for blocking interactions
                const observer = new MutationObserver((mutations) => {
                    mutations.forEach((mutation) => {
                        if (mutation.type === 'attributes' && mutation.attributeName === 'style') {
                            const loadingBar = mutation.target;
                            if (loadingBar.classList.contains('filament-table-loading-bar')) {
                                const isVisible = loadingBar.style.display !== 'none' &&
                                    loadingBar.offsetParent !== null;

                                if (isVisible) {
                                    blockTableInteractions(container);
                                } else {
                                    unblockTableInteractions(container);
                                }
                            }
                        }
                    });
                });

                // Observe the loading bar for style changes
                observer.observe(loadingBar, {
                    attributes: true,
                    attributeFilter: ['style', 'class']
                });
            });
        };

        // Initialize loading bars
        addLoadingBarToTables();

        // Add Livewire event listeners for better blocking control
        if (window.Livewire) {
            // Block interactions when Livewire starts processing
            Livewire.hook('request', ({ uri, options, payload, respond, succeed, fail }) => {
                // Check if this is a table-related request
                const tableTargets = ['gotoPage', 'previousPage', 'nextPage', 'sortTable', 'tableSearch', 'tableFilters', 'tableRecordsPerPage'];
                const isTableRequest = tableTargets.some(target =>
                    payload.updates && payload.updates.some(update =>
                        update.type === 'callMethod' && update.payload.method === target
                    )
                );

                if (isTableRequest) {
                    // Block all table containers immediately
                    document.querySelectorAll('.fi-ta-ctn').forEach(container => {
                        blockTableInteractions(container);
                    });
                }
            });

            // Unblock interactions when Livewire finishes processing
            Livewire.hook('response', ({ status, response }) => {
                // Small delay to ensure DOM updates are complete
                setTimeout(() => {
                    document.querySelectorAll('.fi-ta-ctn').forEach(container => {
                        unblockTableInteractions(container);
                    });
                }, 100);
            });

            // Re-initialize when Livewire updates the DOM
            Livewire.hook('morph.updated', () => {
                setTimeout(addLoadingBarToTables, 100);
            });

            // Handle navigation events
            Livewire.hook('navigated', () => {
                // Clear blocked tables on navigation
                blockedTables.clear();
                setTimeout(addLoadingBarToTables, 100);
            });
        }

        // Add click event delegation to prevent rapid clicking
        document.addEventListener('click', function (event) {
            const target = event.target.closest('button, a');
            if (!target) return;

            // Check if this is a pagination or table action button
            const isPaginationButton = target.closest('.fi-ta-pagination, nav[role="navigation"], .fi-ta-pagination-records-per-page-selector');
            const isTableAction = target.closest('.fi-ta-actions, .fi-ta-header-actions, .fi-ta-filters');

            if (isPaginationButton || isTableAction) {
                const tableContainer = target.closest('.fi-ta-ctn');
                if (tableContainer && blockedTables.has(tableContainer)) {
                    event.preventDefault();
                    event.stopPropagation();
                    return false;
                }

                // Add temporary loading state to the clicked button
                if (target.tagName === 'BUTTON' && !target.disabled) {
                    target.classList.add('filament-pagination-loading');

                    // Remove loading state after a short delay (fallback)
                    setTimeout(() => {
                        target.classList.remove('filament-pagination-loading');
                    }, 3000);
                }
            }
        }, true); // Use capture phase to catch events early
    }
</script>