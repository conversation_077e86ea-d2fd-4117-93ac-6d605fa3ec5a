# TripNotificationService Documentation

## Overview

The `TripNotificationService` centralizes all trip-related push notification logic, making the code DRY (Don't Repeat Yourself) and following SOLID principles. This service eliminates code duplication across multiple controllers.

## Benefits

✅ **DRY Principle**: Eliminates duplicated notification code across controllers
✅ **Single Responsibility**: Handles only trip notification logic
✅ **Consistent Error Handling**: Standardized try-catch and logging
✅ **Easy Maintenance**: Changes to notification logic only need to be made in one place
✅ **Type Safety**: Proper parameter validation and error handling
✅ **Code Reduction**: ~200+ lines of duplicated code reduced to simple service calls

## Usage Examples

### 1. Trip Cancellation Notifications

**Before (Duplicated Code):**
```php
// This code was repeated in multiple controllers
if ($cancelledBy === 'rider' && $trip->driver && $trip->driver->user && $trip->driver->user->fcm_token) {
    $notificationData = [
        'title' => 'لقد تم إلغاء الرحلة',
        'description' => 'لقد تم إلغاء الرحلة من قبل الراكب',
    ];

    try {
        $trip->driver->user->notify(new TripCancellationNotification(
            $trip->driver->user->id,
            $notificationData,
            $trip->id,
            $cancelledBy
        ));
        // ... logging code
    } catch (\Exception $notificationException) {
        // ... error handling
    }
}
```

**After (Using Service):**
```php
// Simple one-liner that handles all the logic
app(TripNotificationService::class)->sendCancellationNotification($trip, $cancelledBy);
```

### 2. Trip Status Notifications

**Trip Start:**
```php
app(TripNotificationService::class)->sendTripStartNotification($trip);
```

**Trip Completion:**
```php
app(TripNotificationService::class)->sendTripCompletionNotification($trip);
```

**Driver Arrival:**
```php
app(TripNotificationService::class)->sendDriverArrivalNotification($trip);
```

**Trip Assignment:**
```php
app(TripNotificationService::class)->sendTripAssignmentNotification($trip);
```

**Trip Request:**
```php
app(TripNotificationService::class)->sendTripRequestNotification($driverUser, $tripId);
```

### 3. Custom Notifications

For custom notification scenarios:
```php
$notificationData = [
    'title' => 'Custom Title',
    'description' => 'Custom Description',
];

app(TripNotificationService::class)->sendTripStatusNotification(
    $trip, 
    'custom_status', 
    $notificationData
);
```

## Service Methods

### Core Methods

- `sendCancellationNotification(Trip $trip, string $cancelledBy)` - Handles both rider and driver cancellations
- `sendTripStatusNotification(Trip $trip, string $status, array $notificationData)` - Generic status notifications
- `sendTripStartNotification(Trip $trip)` - When driver starts the trip
- `sendTripCompletionNotification(Trip $trip)` - When trip is completed
- `sendDriverArrivalNotification(Trip $trip)` - When driver arrives at pickup
- `sendTripAssignmentNotification(Trip $trip)` - When trip is assigned to driver
- `sendTripRequestNotification(User $driverUser, int $tripId)` - When sending trip request to driver

### Features

- **Automatic FCM Token Validation**: Checks if user has valid FCM token before sending
- **Comprehensive Error Handling**: Try-catch blocks with detailed logging
- **Consistent Logging**: Standardized success and error logs
- **Null Safety**: Proper null checks for trip relationships

## Implementation Details

The service automatically:
1. Validates that the target user exists and has an FCM token
2. Creates the appropriate notification object
3. Sends the notification via Laravel's notification system
4. Logs success or failure with relevant details
5. Handles exceptions gracefully without breaking the application flow

## Migration Guide

To migrate existing notification code:

1. **Identify duplicated notification patterns** in your controllers
2. **Replace with service calls** using the appropriate method
3. **Remove the old notification code** and imports
4. **Add the service import**: `use App\Services\TripNotificationService;`

## Completed Refactoring

The following controllers have been successfully refactored:

### ✅ RidersTripController
- **Before**: 58 lines of duplicated cancellation notification code
- **After**: 1 line service call
- **Reduction**: 98% code reduction

### ✅ TripController
- **Before**: 28 lines of duplicated cancellation notification code
- **After**: 1 line service call
- **Reduction**: 96% code reduction

### ✅ DriversTripController
- **Before**: 54 lines of duplicated trip status notification code
- **After**: 2 lines service calls
- **Reduction**: 96% code reduction

### ✅ DriverController
- **Before**: 60 lines of duplicated driver arrival notification code (2 instances)
- **After**: 2 lines service calls
- **Reduction**: 97% code reduction

### ✅ DriverRequestService
- **Before**: 25 lines of duplicated trip request notification code
- **After**: 1 line service call
- **Reduction**: 96% code reduction

**Total Impact**: ~225 lines of duplicated code eliminated across 4 controllers + 1 service!

## Error Handling

The service includes comprehensive error handling:
- Validates user and FCM token existence
- Catches and logs notification exceptions
- Continues application flow even if notifications fail
- Provides detailed error logs for debugging

## Future Enhancements

The service can be easily extended to support:
- Email notifications
- SMS notifications  
- In-app notifications
- Notification preferences
- Notification templates
- Multi-language support
