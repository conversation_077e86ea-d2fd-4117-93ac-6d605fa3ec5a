<?php

namespace Database\Seeders;

use App\Models\Trip;
use Illuminate\Database\Seeder;

class TestTripDurationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     * This seeder creates specific trips to test the duration display functionality.
     */
    public function run(): void
    {
        // Create a completed trip with all timing data for testing duration display
        Trip::factory()->create([
            'status' => 'completed',
            'driver_started_at' => now()->subHours(2), // Driver accepted 2 hours ago
            'actual_departure_time' => now()->subHours(2)->addMinutes(15), // Trip started 15 minutes after acceptance
            'actual_arrival_time' => now()->subHours(1), // Trip completed 1 hour after start
        ]);

        // Create another completed trip with different timing
        Trip::factory()->create([
            'status' => 'completed',
            'driver_started_at' => now()->subHours(3), // Driver accepted 3 hours ago
            'actual_departure_time' => now()->subHours(3)->addMinutes(30), // Trip started 30 minutes after acceptance
            'actual_arrival_time' => now()->subMinutes(30), // Trip completed 2.5 hours after start
        ]);

        // Create a canceled trip that had started
        Trip::factory()->create([
            'status' => 'canceled',
            'cancelled_by' => 'rider',
            'cancellation_stage' => 'onTrip',
            'driver_started_at' => now()->subHours(1), // Driver accepted 1 hour ago
            'actual_departure_time' => now()->subHours(1)->addMinutes(10), // Trip started 10 minutes after acceptance
            'actual_arrival_time' => now()->subMinutes(20), // Trip canceled 30 minutes after start
        ]);

        // Create a canceled trip that never started
        Trip::factory()->create([
            'status' => 'canceled',
            'cancelled_by' => 'driver',
            'cancellation_stage' => 'afterAssigned',
            'driver_started_at' => now()->subMinutes(45), // Driver accepted 45 minutes ago
            'actual_departure_time' => null, // Trip never started
            'actual_arrival_time' => null, // Trip never completed
        ]);

        echo "Created test trips with realistic timing data for duration testing.\n";
        echo "Check the backoffice to see the new duration fields in action!\n";
    }
}
