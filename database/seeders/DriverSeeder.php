<?php

namespace Database\Seeders;

use App\Enums\Drivers\DriverGlobalStatus;
use App\Enums\Vehicles\VehicleStatus;
use App\Models\Driver;
use App\Models\User;
use App\Models\Vehicle;
use Illuminate\Database\Seeder;

class DriverSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('🚗 Creating drivers distributed throughout the year for testing reports...');

        // First, ensure ALL existing drivers have at least one vehicle
        $this->ensureAllDriversHaveVehicles();

        // Create drivers distributed throughout the year for better testing
        $this->createDriversDistributedThroughoutYear();

        // 1. Drivers with in_progress status and one vehicle each (20 drivers) - distributed throughout year
        Driver::factory()
            ->count(20)
            ->thisYear()
            ->create([
                'global_status' => DriverGlobalStatus::in_progress,
                'average_driver_rating' => fake()->numberBetween(3, 5),
            ])
            ->each(function (Driver $driver) {
                // Create one vehicle with in_progress status
                $vehicle = Vehicle::factory()->create([
                    'global_status' => VehicleStatus::in_progress,
                    'average_vehicle_rating' => fake()->numberBetween(3, 5),
                ]);
                $driver->vehicles()->attach($vehicle);
            });

        // 2. Active drivers with two vehicles each (10 drivers) - distributed throughout year
        // One vehicle is active (accepted), the other is in_progress
        Driver::factory()
            ->count(10)
            ->thisYear()
            ->create([
                'global_status' => DriverGlobalStatus::active,
                'average_driver_rating' => fake()->numberBetween(4, 5),
            ])
            ->each(function (Driver $driver) {
                // Create first vehicle with active status (accepted)
                $activeVehicle = Vehicle::factory()->create([
                    'global_status' => VehicleStatus::active,
                    'average_vehicle_rating' => fake()->numberBetween(4, 5),
                ]);
                $driver->vehicles()->attach($activeVehicle);

                // Create second vehicle with in_progress status
                $inProgressVehicle = Vehicle::factory()->create([
                    'global_status' => VehicleStatus::in_progress,
                    'average_vehicle_rating' => fake()->numberBetween(3, 5),
                ]);
                $driver->vehicles()->attach($inProgressVehicle);
            });

        // 3. Female drivers with in_progress status (8 drivers) - distributed throughout year
        Driver::factory()
            ->count(8)
            ->thisYear()
            ->create([
                'global_status' => DriverGlobalStatus::in_progress,
                'rider_gender' => 'female',
                'user_id' => User::factory()->thisYear()->create([
                    'gender' => 'female',
                    'type' => 'driver',
                ]),
                'average_driver_rating' => fake()->numberBetween(4, 5),
            ])
            ->each(function (Driver $driver) {
                $vehicle = Vehicle::factory()->create([
                    'global_status' => VehicleStatus::in_progress,
                    'is_female_only' => true, // Female-only vehicles
                    'average_vehicle_rating' => fake()->numberBetween(4, 5),
                ]);
                $driver->vehicles()->attach($vehicle);
            });

        // 4. Male-only drivers with in_progress status (8 drivers) - distributed throughout year
        Driver::factory()
            ->count(8)
            ->thisYear()
            ->create([
                'global_status' => DriverGlobalStatus::in_progress,
                'rider_gender' => 'male',
                'average_driver_rating' => fake()->numberBetween(3, 5),
            ])
            ->each(function (Driver $driver) {
                $vehicle = Vehicle::factory()->create([
                    'global_status' => VehicleStatus::in_progress,
                    'is_female_only' => false,
                    'average_vehicle_rating' => fake()->numberBetween(3, 5),
                ]);
                $driver->vehicles()->attach($vehicle);
            });

        // 5. Drivers who accept both genders with in_progress status (12 drivers) - distributed throughout year
        Driver::factory()
            ->count(12)
            ->thisYear()
            ->create([
                'global_status' => DriverGlobalStatus::in_progress,
                'rider_gender' => 'both',
                'average_driver_rating' => fake()->numberBetween(3, 5),
            ])
            ->each(function (Driver $driver) {
                $vehicle = Vehicle::factory()->create([
                    'global_status' => VehicleStatus::in_progress,
                    'is_female_only' => fake()->boolean(30), // 30% chance of being female-only
                    'average_vehicle_rating' => fake()->numberBetween(3, 5),
                ]);
                $driver->vehicles()->attach($vehicle);
            });
    }

    /**
     * Ensure all existing drivers have at least one vehicle
     */
    private function ensureAllDriversHaveVehicles(): void
    {
        $driversWithoutVehicles = Driver::with('vehicles')
            ->get()
            ->filter(fn (Driver $driver) => $driver->vehicles->count() === 0);

        foreach ($driversWithoutVehicles as $driver) {
            // Determine vehicle status based on driver status
            $vehicleStatus = match ($driver->global_status) {
                DriverGlobalStatus::active => VehicleStatus::active,
                DriverGlobalStatus::in_progress => VehicleStatus::in_progress,
                DriverGlobalStatus::blocked => VehicleStatus::blocked,
                DriverGlobalStatus::rejected => VehicleStatus::rejected,
                default => VehicleStatus::pending,
            };

            // Create a vehicle for this driver
            $vehicle = Vehicle::factory()->create([
                'global_status' => $vehicleStatus,
                'average_vehicle_rating' => fake()->numberBetween(3, 5),
            ]);

            // Attach the vehicle to the driver
            $driver->vehicles()->attach($vehicle);
        }

        $this->command->info("✅ Ensured {$driversWithoutVehicles->count()} drivers have vehicles");
    }

    /**
     * Create drivers distributed throughout the year for better report testing
     */
    private function createDriversDistributedThroughoutYear(): void
    {
        $this->command->info('📅 Creating drivers distributed throughout the year...');

        // Create drivers for each month of the current year
        for ($month = 1; $month <= 12; $month++) {
            $monthName = now()->setMonth($month)->format('F');
            $driversCount = fake()->numberBetween(3, 8); // Random number of drivers per month

            $this->command->info("   Creating {$driversCount} drivers for {$monthName}...");

            Driver::factory()
                ->count($driversCount)
                ->forMonth($month)
                ->create([
                    'global_status' => fake()->randomElement([
                        DriverGlobalStatus::active,
                        DriverGlobalStatus::active,
                        DriverGlobalStatus::active, // More active drivers
                        DriverGlobalStatus::in_progress,
                        DriverGlobalStatus::blocked,
                    ]),
                    'average_driver_rating' => fake()->numberBetween(3, 5),
                ])
                ->each(function (Driver $driver) {
                    // Create one vehicle for each driver
                    $vehicleStatus = match ($driver->global_status) {
                        DriverGlobalStatus::active => VehicleStatus::active,
                        DriverGlobalStatus::in_progress => VehicleStatus::in_progress,
                        DriverGlobalStatus::blocked => VehicleStatus::blocked,
                        default => VehicleStatus::pending,
                    };

                    $vehicle = Vehicle::factory()->create([
                        'global_status' => $vehicleStatus,
                        'average_vehicle_rating' => fake()->numberBetween(3, 5),
                    ]);
                    $driver->vehicles()->attach($vehicle);
                });
        }

        // Create some drivers for this week specifically (for testing weekly reports)
        $this->command->info('   Creating additional drivers for this week...');
        Driver::factory()
            ->count(5)
            ->thisWeek()
            ->create([
                'global_status' => DriverGlobalStatus::active,
                'average_driver_rating' => fake()->numberBetween(4, 5),
            ])
            ->each(function (Driver $driver) {
                $vehicle = Vehicle::factory()->create([
                    'global_status' => VehicleStatus::active,
                    'average_vehicle_rating' => fake()->numberBetween(4, 5),
                ]);
                $driver->vehicles()->attach($vehicle);
            });

        // Create some drivers for this month specifically (for testing monthly reports)
        $this->command->info('   Creating additional drivers for this month...');
        Driver::factory()
            ->count(8)
            ->thisMonth()
            ->create([
                'global_status' => fake()->randomElement([
                    DriverGlobalStatus::active,
                    DriverGlobalStatus::in_progress,
                ]),
                'average_driver_rating' => fake()->numberBetween(3, 5),
            ])
            ->each(function (Driver $driver) {
                $vehicleStatus = $driver->global_status === DriverGlobalStatus::active
                    ? VehicleStatus::active
                    : VehicleStatus::in_progress;

                $vehicle = Vehicle::factory()->create([
                    'global_status' => $vehicleStatus,
                    'average_vehicle_rating' => fake()->numberBetween(3, 5),
                ]);
                $driver->vehicles()->attach($vehicle);
            });

        $this->command->info('✅ Completed creating drivers distributed throughout the year');
    }
}
