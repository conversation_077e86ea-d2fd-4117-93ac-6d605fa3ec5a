<?php

namespace Database\Seeders;

use App\Enums\Drivers\DriverGlobalStatus;
use App\Enums\Vehicles\VehicleStatus;
use App\Models\Driver;
use App\Models\User;
use App\Models\Vehicle;
use Illuminate\Database\Seeder;

class ReportsTestSeeder extends Seeder
{
    /**
     * Run the database seeds.
     * This seeder creates drivers distributed throughout the year for testing reports functionality.
     */
    public function run(): void
    {
        $this->command->info('🧪 Creating test data for reports functionality...');
        
        // Clear existing test data if needed
        $this->command->info('📊 Creating drivers distributed throughout the year...');
        
        // Create drivers for each month with varying quantities
        $monthlyDriverCounts = [
            1 => 12,  // January - High registration month
            2 => 8,   // February
            3 => 15,  // March - Peak registration
            4 => 10,  // April
            5 => 6,   // May - Lower registration
            6 => 9,   // June
            7 => 11,  // July
            8 => 7,   // August - Summer low
            9 => 14,  // September - Back to school boost
            10 => 13, // October
            11 => 9,  // November
            12 => 16, // December - Year-end high
        ];

        $totalDrivers = 0;
        
        foreach ($monthlyDriverCounts as $month => $count) {
            $monthName = now()->setMonth($month)->format('F');
            $this->command->info("   📅 Creating {$count} drivers for {$monthName}...");
            
            Driver::factory()
                ->count($count)
                ->forMonth($month)
                ->create([
                    'global_status' => fake()->randomElement([
                        DriverGlobalStatus::active,
                        DriverGlobalStatus::active,
                        DriverGlobalStatus::active,     // 60% active
                        DriverGlobalStatus::in_progress, // 20% in progress
                        DriverGlobalStatus::blocked,     // 20% blocked
                    ]),
                    'average_driver_rating' => fake()->numberBetween(3, 5),
                ])
                ->each(function (Driver $driver) {
                    // Create vehicle for each driver
                    $vehicleStatus = match ($driver->global_status) {
                        DriverGlobalStatus::active => VehicleStatus::active,
                        DriverGlobalStatus::in_progress => VehicleStatus::in_progress,
                        DriverGlobalStatus::blocked => VehicleStatus::blocked,
                        default => VehicleStatus::pending,
                    };

                    $vehicle = Vehicle::factory()->create([
                        'global_status' => $vehicleStatus,
                        'average_vehicle_rating' => fake()->numberBetween(3, 5),
                    ]);
                    
                    $driver->vehicles()->attach($vehicle);
                });
                
            $totalDrivers += $count;
        }

        // Create additional drivers for this week (for testing weekly reports)
        $this->command->info('   📅 Creating additional drivers for this week...');
        $weeklyDrivers = Driver::factory()
            ->count(8)
            ->thisWeek()
            ->create([
                'global_status' => fake()->randomElement([
                    DriverGlobalStatus::active,
                    DriverGlobalStatus::in_progress,
                ]),
                'average_driver_rating' => fake()->numberBetween(4, 5),
            ]);
            
        $weeklyDrivers->each(function (Driver $driver) {
            $vehicleStatus = $driver->global_status === DriverGlobalStatus::active 
                ? VehicleStatus::active 
                : VehicleStatus::in_progress;

            $vehicle = Vehicle::factory()->create([
                'global_status' => $vehicleStatus,
                'average_vehicle_rating' => fake()->numberBetween(4, 5),
            ]);
            
            $driver->vehicles()->attach($vehicle);
        });

        // Create additional drivers for this month (for testing monthly reports)
        $this->command->info('   📅 Creating additional drivers for this month...');
        $monthlyDrivers = Driver::factory()
            ->count(12)
            ->thisMonth()
            ->create([
                'global_status' => fake()->randomElement([
                    DriverGlobalStatus::active,
                    DriverGlobalStatus::active,
                    DriverGlobalStatus::in_progress,
                ]),
                'average_driver_rating' => fake()->numberBetween(3, 5),
            ]);
            
        $monthlyDrivers->each(function (Driver $driver) {
            $vehicleStatus = $driver->global_status === DriverGlobalStatus::active 
                ? VehicleStatus::active 
                : VehicleStatus::in_progress;

            $vehicle = Vehicle::factory()->create([
                'global_status' => $vehicleStatus,
                'average_vehicle_rating' => fake()->numberBetween(3, 5),
            ]);
            
            $driver->vehicles()->attach($vehicle);
        });

        $totalDrivers += 8 + 12; // Add weekly and monthly drivers

        $this->command->info("✅ Successfully created {$totalDrivers} drivers distributed throughout the year");
        $this->command->info('🎯 You can now test the reports functionality with:');
        $this->command->info('   - Total (All Time): All ' . $totalDrivers . ' drivers');
        $this->command->info('   - This Week: 8 drivers');
        $this->command->info('   - This Month: 12 + monthly distribution drivers');
        $this->command->info('   - Custom Date Range: Any range within the current year');
    }
}
