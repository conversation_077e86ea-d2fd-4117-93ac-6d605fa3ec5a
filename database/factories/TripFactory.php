<?php

namespace Database\Factories;

use App\Enums\Trips\CancellationStage;
use App\Enums\Trips\TripStatus;
use App\Models\Area;
use App\Models\Driver;
use App\Models\Rider;
use App\Models\Trip;
use App\Models\TripLocation;
use App\Models\Vehicle;
use Illuminate\Database\Eloquent\Factories\Factory;

class TripFactory extends Factory
{
    protected $model = Trip::class;

    public function definition(): array
    {
        // Define possible trip statuses
        $statuses = [
            TripStatus::pending->value,
            TripStatus::timeout->value,
            TripStatus::dispatched->value,
            TripStatus::canceled->value,
            TripStatus::rejected->value,
            TripStatus::assigned->value,
            TripStatus::driver_arriving->value,
            TripStatus::driver_arrived->value,
            TripStatus::no_show->value,
            TripStatus::on_trip->value,
            TripStatus::waiting_for_driver_confirmation->value,
            TripStatus::completed->value,
        ];

        $status = fake()->randomElement($statuses);

        // Generate realistic trip times with proper progression
        $estimatedDepartureTime = fake()->dateTimeBetween('-2 hours', '+1 hour');

        // Driver acceptance time (driver_started_at) - set for assigned, completed, or canceled trips
        $driverStartedAt = null;
        $actualDepartureTime = null;
        $actualArrivalTime = null;

        if (in_array($status, [
            TripStatus::assigned->value,
            TripStatus::driver_arriving->value,
            TripStatus::driver_arrived->value,
            TripStatus::on_trip->value,
            TripStatus::waiting_for_driver_confirmation->value,
            TripStatus::completed->value,
            TripStatus::canceled->value,
        ])) {
            // Driver accepted the trip 5-30 minutes after estimated departure
            $driverStartedAt = fake()->dateTimeBetween($estimatedDepartureTime, $estimatedDepartureTime->format('Y-m-d H:i:s').' +30 minutes');

            // For trips that have started or completed
            if (in_array($status, [
                TripStatus::on_trip->value,
                TripStatus::waiting_for_driver_confirmation->value,
                TripStatus::completed->value,
            ])) {
                // Trip actually started 10-60 minutes after driver acceptance
                $actualDepartureTime = fake()->dateTimeBetween($driverStartedAt, $driverStartedAt->format('Y-m-d H:i:s').' +60 minutes');

                // For completed trips, set arrival time
                if ($status === TripStatus::completed->value) {
                    // Trip completed 15-120 minutes after departure
                    $actualArrivalTime = fake()->dateTimeBetween($actualDepartureTime, $actualDepartureTime->format('Y-m-d H:i:s').' +120 minutes');
                }
            }

            // For canceled trips after assignment
            if ($status === TripStatus::canceled->value) {
                // Some canceled trips might have started
                if (fake()->boolean(30)) { // 30% chance the trip had started before cancellation
                    $actualDepartureTime = fake()->dateTimeBetween($driverStartedAt, $driverStartedAt->format('Y-m-d H:i:s').' +60 minutes');
                    // Canceled during trip
                    $actualArrivalTime = fake()->dateTimeBetween($actualDepartureTime, $actualDepartureTime->format('Y-m-d H:i:s').' +30 minutes');
                }
            }
        }

        $estimatedArrivalTime = fake()->dateTimeBetween($estimatedDepartureTime, $estimatedDepartureTime->format('Y-m-d H:i:s').' +3 hours');

        // Generate realistic pricing
        $baseFare = fake()->randomFloat(2, 5, 50);
        $distanceFare = fake()->randomFloat(2, 1, 20);
        $surgeMultiplier = fake()->randomFloat(2, 1, 3);
        $total = round($baseFare + $distanceFare * $surgeMultiplier, 2);

        // Only set cancellation fields if trip was canceled
        $isCancelled = $status === TripStatus::canceled->value;
        $cancellationStages = [
            CancellationStage::afterAssigned->value,
            CancellationStage::afterDispatch->value,
            CancellationStage::onPickup->value,
            CancellationStage::onTrip->value,
        ];

        // Sample rider notes
        $riderNotesSamples = [
            "I'm behind the central market",
            'Near the big white mosque',
            'Waiting by the gas station',
            'At the bus stop across the mall',
            'Just past the roundabout',
            'Next to the blue building',
            'In front of the pharmacy',
            'By the traffic light',
        ];

        // Generate a random distance between 1 and 50 km
        $distance = fake()->randomFloat(2, 1, 50);

        return [
            'status' => $status,
            'distance' => $distance,
            'estimated_departure_time' => $estimatedDepartureTime,
            'actual_departure_time' => $actualDepartureTime,
            'estimated_arrival_time' => $estimatedArrivalTime,
            'actual_arrival_time' => $actualArrivalTime,
            'driver_started_at' => $driverStartedAt,
            'pricing_breakdown' => json_encode([
                'base_fare' => $baseFare,
                'distance_fare' => $distanceFare,
                'surge_multiplier' => $surgeMultiplier,
                'total' => $total,
                'distance' => $distance,
                'currency' => 'LYD',
            ]),
            'rider_id' => function () {
                // Make sure we have at least one rider
                if (Rider::count() === 0) {
                    return Rider::factory()->create()->id;
                }

                return Rider::inRandomOrder()->first()->id;
            },
            'driver_id' => function () {
                // Make sure we have at least one driver
                if (Driver::count() === 0) {
                    return Driver::factory()->create()->id;
                }

                return Driver::inRandomOrder()->first()->id;
            },
            'vehicle_id' => function () {
                // Make sure we have at least one vehicle
                if (Vehicle::count() === 0) {
                    return Vehicle::factory()->create()->id;
                }

                return Vehicle::inRandomOrder()->first()->id;
            },
            'departure_area_id' => function () {
                // Make sure we have at least one area
                if (Area::count() === 0) {
                    return Area::factory()->create()->id;
                }

                return Area::inRandomOrder()->first()->id;
            },
            'arrival_area_id' => function () {
                // Make sure we have at least one area
                if (Area::count() === 0) {
                    return Area::factory()->create()->id;
                }

                return Area::inRandomOrder()->first()->id;
            },
            'cancelled_by' => $isCancelled ? fake()->randomElement(['rider', 'driver']) : null,
            'cancellation_stage' => $isCancelled ? fake()->randomElement($cancellationStages) : null,
            'rider_notes' => fake()->optional(0.7)->randomElement($riderNotesSamples),
            'is_female' => fake()->boolean(20), // 20% chance of being female-only trip
        ];
    }

    /**
     * Configure the model factory.
     *
     * @return $this
     */
    public function configure()
    {
        return $this->afterCreating(function (Trip $trip) {
            // Create a trip location for this trip
            $tripLocation = TripLocation::factory()->create([
                'trip_id' => $trip->id,
            ]);

            // Create a trip vehicle type for this trip
            $tripVehicleType = \App\Models\TripVehicleType::create([
                'trip_id' => $trip->id,
                'vehicle_category' => fake()->randomElement(['passenger', 'freight']),
                'seat_number' => fake()->numberBetween(2, 8),
                'vehicle_equipments' => fake()->optional()->randomElement(['1,2', '2,3', '1,3']),
                'is_covered' => fake()->boolean(),
                'weight_category' => fake()->randomElement(['light', 'medium', 'heavy']),
            ]);

            // Update the trip with the trip_location_id and vehicle_type_id
            $trip->update([
                'trip_location_id' => $tripLocation->id,
                'vehicle_type_id' => $tripVehicleType->id,
            ]);
        });
    }
}
