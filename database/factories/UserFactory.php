<?php

namespace Database\Factories;

use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

class UserFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = User::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        // Use Faker with Arabic locale for Arabic names
        $arabicFaker = \Faker\Factory::create('ar_SA');

        return [
            'name' => $arabicFaker->firstName(),
            'last_name' => $arabicFaker->lastName(),
            'email' => fake()->unique()->safeEmail(),
            'email_verified_at' => now(),
            'password' => Hash::make('password'),
            'remember_token' => Str::random(10),
            'type' => fake()->randomElement(['passenger', 'driver', 'admin']),
            'phone_number' => '+218'.fake()->unique()->numberBetween(910000000, 949999999),
            'cover_picture' => fake()->optional()->imageUrl(),
            'gender' => fake()->randomElement(['male', 'female']),
            // 'address' => fake()->randomElement([
            //     'Tripoli, Al-Dahra Street',
            //     'Benghazi, Al-Fuwaihat District',
            //     'Misrata, Al-Zarrouq Street',
            //     'Zawiya, Al-Shuhada Road',
            //     'Sabha, Al-Qurayyat Neighborhood',
            //     'Zliten, Al-Manara Street',
            //     'Derna, Al-Maghar District',
            //     'Ajdabiya, Al-Manasir Street',
            //     'Ghadames, City Center',
            // ]),

        ];
    }

    /**
     * Indicate that the model's email address should be unverified.
     */
    public function unverified(): Factory
    {
        return $this->state(function (array $attributes) {
            return [
                'email_verified_at' => null,
            ];
        });
    }

    /**
     * Create users distributed throughout a specific time period
     */
    public function distributedBetween($startDate, $endDate): Factory
    {
        return $this->state(function (array $attributes) use ($startDate, $endDate) {
            return [
                'created_at' => fake()->dateTimeBetween($startDate, $endDate),
                'email_verified_at' => fake()->dateTimeBetween($startDate, $endDate),
            ];
        });
    }

    /**
     * Create users for a specific month
     */
    public function forMonth(int $month, ?int $year = null): Factory
    {
        $year = $year ?? now()->year;
        $startDate = now()->setYear($year)->setMonth($month)->startOfMonth();
        $endDate = now()->setYear($year)->setMonth($month)->endOfMonth();

        return $this->distributedBetween($startDate, $endDate);
    }

    /**
     * Create users for this week
     */
    public function thisWeek(): Factory
    {
        return $this->distributedBetween(now()->startOfWeek(), now()->endOfWeek());
    }

    /**
     * Create users for this month
     */
    public function thisMonth(): Factory
    {
        return $this->distributedBetween(now()->startOfMonth(), now()->endOfMonth());
    }

    /**
     * Create users for the entire current year
     */
    public function thisYear(): Factory
    {
        return $this->distributedBetween(now()->startOfYear(), now());
    }
}
