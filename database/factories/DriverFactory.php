<?php

namespace Database\Factories;

use App\Enums\Drivers\DriverGlobalStatus;
use App\Enums\Vehicles\VehicleStatus;
use App\Models\Driver;
use App\Models\Vehicle;
use Illuminate\Database\Eloquent\Factories\Factory;

class DriverFactory extends Factory
{
    protected $model = Driver::class;

    public function definition(): array
    {
        return [
            'id_number' => fake()->unique()->numerify('############'),
            'rider_gender' => fake()->randomElement(['female', 'male', 'both']),
            'average_driver_rating' => fake()->numberBetween(1, 5),
            'global_status' => fake()->randomElement(DriverGlobalStatus::cases()),
            'user_id' => \App\Models\User::factory(),
            // Distribute creation dates throughout the current year
            'created_at' => fake()->dateTimeBetween(
                now()->startOfYear(),
                now()
            ),
        ];
    }

    public function withVehicles(int $vehicleCount = 1): Factory
    {
        return $this->afterCreating(function (Driver $driver) use ($vehicleCount) {
            $vehicles = Vehicle::factory($vehicleCount)->create();
            $driver->vehicles()->attach($vehicles);
        });
    }

    /**
     * Create drivers distributed throughout a specific time period
     */
    public function distributedBetween($startDate, $endDate): Factory
    {
        return $this->state(function (array $attributes) use ($startDate, $endDate) {
            $createdAt = fake()->dateTimeBetween($startDate, $endDate);

            return [
                'created_at' => $createdAt,
                'user_id' => \App\Models\User::factory()->state([
                    'type' => 'driver',
                    'created_at' => $createdAt,
                    'email_verified_at' => $createdAt,
                ]),
            ];
        });
    }

    /**
     * Create drivers for a specific month
     */
    public function forMonth(int $month, ?int $year = null): Factory
    {
        $year = $year ?? now()->year;
        $startDate = now()->setYear($year)->setMonth($month)->startOfMonth();
        $endDate = now()->setYear($year)->setMonth($month)->endOfMonth();

        return $this->distributedBetween($startDate, $endDate);
    }

    /**
     * Create drivers for this week
     */
    public function thisWeek(): Factory
    {
        return $this->distributedBetween(now()->startOfWeek(), now()->endOfWeek());
    }

    /**
     * Create drivers for this month
     */
    public function thisMonth(): Factory
    {
        return $this->distributedBetween(now()->startOfMonth(), now()->endOfMonth());
    }

    /**
     * Create drivers for the entire current year
     */
    public function thisYear(): Factory
    {
        return $this->distributedBetween(now()->startOfYear(), now());
    }

    public function inProgress(): Factory
    {
        return $this->state(fn () => [
            'global_status' => DriverGlobalStatus::in_progress,
        ]);
    }

    public function active(): Factory
    {
        return $this->state(fn () => [
            'global_status' => DriverGlobalStatus::active,
        ]);
    }

    public function pending(): Factory
    {
        return $this->state(fn () => [
            'global_status' => DriverGlobalStatus::pending,
        ]);
    }

    public function withActiveAndInProgressVehicles(): Factory
    {
        return $this->afterCreating(function (Driver $driver) {
            // Create one active vehicle
            $activeVehicle = Vehicle::factory()->create([
                'global_status' => VehicleStatus::active,
                'average_vehicle_rating' => fake()->numberBetween(4, 5),
            ]);
            $driver->vehicles()->attach($activeVehicle);

            // Create one in_progress vehicle
            $inProgressVehicle = Vehicle::factory()->create([
                'global_status' => VehicleStatus::in_progress,
                'average_vehicle_rating' => fake()->numberBetween(3, 5),
            ]);
            $driver->vehicles()->attach($inProgressVehicle);
        });
    }
}
